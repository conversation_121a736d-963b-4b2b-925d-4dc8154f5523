package com.ikonnected.compose.ui.handler

import androidx.compose.foundation.lazy.LazyListLayoutInfo
import androidx.compose.foundation.lazy.LazyListState
import com.ikonnected.compose.ui.handler.model.ListState

private const val PaginationOffset = 5

fun LazyListState.canLoadNextPage(
    listState: ListState,
    offset: Int = PaginationOffset
) = when {
    listState != ListState.Idle -> false
    layoutInfo.totalItemsCount == 0 -> false
    firstVisibleItemIndex == 0 -> false // Fix Zero pixel issue
    else -> layoutInfo.isLastVisible(offset = offset)
}

private fun LazyListLayoutInfo.isLastVisible(offset: Int = 0) =
    when (val lastVisibleIndex = visibleItemsInfo.lastOrNull()?.index) {
        is Int -> lastVisibleIndex.inc() >= totalItemsCount - offset
        else -> false
    }