package com.ikonnected.compose.ui.element.image

import ikonnected.compose_ui.generated.resources.Res
import ikonnected.compose_ui.generated.resources.ic_archive
import ikonnected.compose_ui.generated.resources.ic_bar_code
import ikonnected.compose_ui.generated.resources.ic_caution
import ikonnected.compose_ui.generated.resources.ic_caution_reg
import ikonnected.compose_ui.generated.resources.ic_check
import ikonnected.compose_ui.generated.resources.ic_close
import ikonnected.compose_ui.generated.resources.ic_home
import ikonnected.compose_ui.generated.resources.ic_lock
import ikonnected.compose_ui.generated.resources.ic_logo
import ikonnected.compose_ui.generated.resources.ic_menu
import ikonnected.compose_ui.generated.resources.ic_notification
import ikonnected.compose_ui.generated.resources.ic_options_vertical
import ikonnected.compose_ui.generated.resources.ic_payment_successful
import ikonnected.compose_ui.generated.resources.ic_person
import ikonnected.compose_ui.generated.resources.ic_person_outlined
import ikonnected.compose_ui.generated.resources.ic_sold
import ikonnected.compose_ui.generated.resources.ic_total_voucher
import ikonnected.compose_ui.generated.resources.ic_voucher
import ikonnected.compose_ui.generated.resources.ic_wallet
import ikonnected.compose_ui.generated.resources.sc_icon_eye_off
import ikonnected.compose_ui.generated.resources.sc_icon_eye_on


object Icons {
    val archive = Image.Drawable(Res.drawable.ic_archive)
    val barCode = Image.Drawable(Res.drawable.ic_bar_code)
    val caution = Image.Drawable(Res.drawable.ic_caution)
    val cautionRegular = Image.Drawable(Res.drawable.ic_caution_reg)
    val check = Image.Drawable(Res.drawable.ic_check)
    val close = Image.Drawable(Res.drawable.ic_close)

    val home = Image.Drawable(Res.drawable.ic_home)
    val lock = Image.Drawable(Res.drawable.ic_lock)
    val logo = Image.Drawable(Res.drawable.ic_logo)
    val menu = Image.Drawable(Res.drawable.ic_menu)
    val notification = Image.Drawable(Res.drawable.ic_notification)
    val option = Image.Drawable(Res.drawable.ic_options_vertical)
    val payment = Image.Drawable(Res.drawable.ic_payment_successful)
    val person = Image.Drawable(Res.drawable.ic_person)
    val personOutlined = Image.Drawable(Res.drawable.ic_person_outlined)
    val sold = Image.Drawable(Res.drawable.ic_sold)
    val totalVoucher = Image.Drawable(Res.drawable.ic_total_voucher)
    val voucher = Image.Drawable(Res.drawable.ic_voucher)
    val wallet = Image.Drawable(Res.drawable.ic_wallet)
    val eyeOff = Image.Drawable(Res.drawable.sc_icon_eye_off)
    val eyeOn = Image.Drawable(Res.drawable.sc_icon_eye_on)



}