package com.ikonnected.compose.ui.component.switch.internals

import androidx.compose.material3.SwitchDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.graphics.Color
import androidx.compose.runtime.compositionLocalOf

internal data class SwitchColors(
    val checkedThumbColor: Color = Color.Unspecified,
    val uncheckedThumbColor: Color = Color.Unspecified,
    val checkedTrackColor: Color = Color.Unspecified,
    val uncheckedTrackColor: Color = Color.Unspecified,
    )

internal  val LocalSwitchColorProvider = compositionLocalOf { SwitchColors() }

@Composable
internal fun ProvideSwitchColors(
    colors: SwitchColors,
    content: @Composable () -> Unit
) = CompositionLocalProvider(LocalSwitchColorProvider provides colors, content = content)




@Composable
internal fun SwitchColors.toSwitchColors() = SwitchDefaults.colors(
    checkedThumbColor = checkedThumbColor,
    checkedTrackColor = checkedTrackColor
)
