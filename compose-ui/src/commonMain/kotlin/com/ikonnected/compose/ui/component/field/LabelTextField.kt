package com.ikonnected.compose.ui.component.field

import androidx.compose.animation.animateColorAsState
import androidx.compose.foundation.interaction.FocusInteraction
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.PressInteraction
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.text.selection.LocalTextSelectionColors
import androidx.compose.foundation.text.selection.TextSelectionColors
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.focus.focusProperties
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.ikonnected.compose.ui.component.field.internal.LocalTextFieldColorsProvider
import com.ikonnected.compose.ui.component.field.internal.ProvideTextFieldColors
import com.ikonnected.compose.ui.component.field.internal.componentColors
import com.ikonnected.compose.ui.component.field.model.TextInputModel
import com.ikonnected.compose.ui.element.text.Text
import com.ikonnected.compose.ui.theme.Theme
import com.ikonnected.compose.ui.theme.isSystemInDarkTheme


@Composable
fun LabelTextField(
    model: TextInputModel,
    readOnly: Boolean = false,
    singleLine: Boolean = true,
    minLine: Int = 1,
    maxLine: Int = 1,
    isBorderLess: Boolean = false,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions(),
    modifier: Modifier = Modifier,
    visualTransformation: VisualTransformation = VisualTransformation.None,
    onValueChange: (String) -> Unit,
    leadingContent: @Composable (RowScope.() -> Unit)? = null,
    onFocusChanged: ((Boolean) -> Unit)? = null,
    onPressedChanged: ((Boolean) -> Unit)? = null,
    trailingContent: @Composable (() -> Unit)? = null,
    invertedColors: Boolean = isSystemInDarkTheme()
) {

    val interactionSource = remember { MutableInteractionSource() }
    val isError by remember(model.isError) { mutableStateOf(model.isError) }

    val showPlaceholder = remember(model) { model.value.isEmpty() }

    var isFocused by remember { mutableStateOf(false) }

    LaunchedEffect(interactionSource) {
        interactionSource.interactions.collect { interaction ->
            when (interaction) {
                is PressInteraction.Press -> onPressedChanged?.invoke(true)
                is FocusInteraction.Focus -> {
                    onFocusChanged?.invoke(true)
                    isFocused = true
                }
                is FocusInteraction.Unfocus -> {
                    onFocusChanged?.invoke(false)
                    isFocused = false
                }
            }
        }
    }

    ProvideTextFieldColors(colors = componentColors(invertedColors)) {
        val animatedColor by animateColorAsState(
            when {
                isFocused && !isError -> LocalTextFieldColorsProvider.current.focusedIndicatorColor
                isError -> LocalTextFieldColorsProvider.current.errorColor
                else -> LocalTextFieldColorsProvider.current.unfocusedIndicatorColor
            },
            label = "color"
        )

        val animatedBorderlessColor by animateColorAsState(
            when {
                isFocused && !isError -> LocalTextFieldColorsProvider.current.focusedIndicatorColor
                isError -> LocalTextFieldColorsProvider.current.errorColor
                else -> LocalTextFieldColorsProvider.current.unfocusedBorderLessColor
            },
            label = "color"
        )

        Column(
            horizontalAlignment = Alignment.Start,
            modifier = modifier
        ) {
            model.label?.let {
                Text(
                    clause = model.label,
                    style = Theme.typography.body.copy(color = LocalTextFieldColorsProvider.current.labelColor)
                )
                Spacer(modifier = Modifier.height(Theme.spacing.x3))
            }
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .drawBehind {
                        drawRoundRect(
                            color = if(isBorderLess) animatedBorderlessColor else animatedColor,
                                cornerRadius = CornerRadius(x = 10.dp.toPx(), y = 10.dp.toPx())
                        )
                        drawRoundRect(
                            color = Color(0xFFFAFAFA),
                            topLeft = Offset(2.dp.toPx(), 2.dp.toPx()),
                            size = Size(
                                width = size.width - 4.dp.toPx(),
                                height = size.height - 4.dp.toPx()
                            ),
                            cornerRadius = CornerRadius(
                                x = 8.dp.toPx(),
                                y = 8.dp.toPx()
                            )
                        )
                    }
                    .padding(all = Theme.spacing.x4),
                verticalAlignment = Alignment.CenterVertically
            ) {
                leadingContent?.let { leadingContent ->
                    leadingContent.invoke(this)
                    Spacer(modifier = Modifier.width(Theme.spacing.x2))
                }
                Box(
                    modifier = Modifier
                        .weight(1f)
                ) {
                    if (showPlaceholder) {
                        Text(
                            clause = model.placeholder,
                            style = Theme.typography.bodyS.copy(color = LocalTextFieldColorsProvider.current.placeholderColor),
                            modifier = Modifier.focusProperties { canFocus = false }
                        )
                    }
                    CompositionLocalProvider(LocalTextSelectionColors provides provideTextSelectionColor(invertedColors)) {
                        BasicTextField(
                            value = model.value,
                            onValueChange = onValueChange,
                            textStyle = Theme.typography.bodyS,
                            modifier = Modifier.fillMaxWidth(),
                            singleLine = singleLine,
                            minLines = minLine,
                            maxLines = maxLine,
                            readOnly = readOnly,
                            cursorBrush = SolidColor(LocalTextFieldColorsProvider.current.cursorColor),
                            interactionSource = interactionSource,
                            keyboardOptions = keyboardOptions,
                            keyboardActions = keyboardActions,
                            visualTransformation = visualTransformation
                        )
                    }
                }
                trailingContent?.invoke()
            }
            Spacer(modifier = Modifier.height(Theme.spacing.x3))
            model.footerText?.let { errorText ->
                Spacer(modifier = Modifier.height(Theme.spacing.x2))
                Text(
                    clause = errorText,
                    style = Theme.typography.bodyXs.copy(color = isError.toTextColor()),
                    lineHeight = 25.sp
                )
            }
        }
    }
}

@Composable
internal fun provideTextSelectionColor(invertedColors: Boolean): TextSelectionColors {
    return TextSelectionColors(
        handleColor = LocalTextFieldColorsProvider.current.cursorColor,
        backgroundColor = LocalTextFieldColorsProvider.current.selectionBackgroundColor
    )
}

@Composable
private fun Boolean.toTextColor() = when(this) {
    true -> LocalTextFieldColorsProvider.current.errorColor
    false -> LocalTextFieldColorsProvider.current.footerTextColor
}