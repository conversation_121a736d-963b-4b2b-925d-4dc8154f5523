package com.ikonnected.compose.ui.component.steptracker

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.ikonnected.compose.ui.component.steptracker.model.StepTrackerModel
import com.ikonnected.compose.ui.component.steptracker.model.StepTrackerType
import com.ikonnected.compose.ui.theme.IkonnectedColors
import com.ikonnected.compose.ui.theme.Theme


@Composable
fun StepTracker(modifier: Modifier, model: StepTrackerModel) {
    when (model.type) {
        StepTrackerType.Default -> DefaultStepTracker(modifier = modifier, model = model)
        StepTrackerType.WithGaps -> GappedStepTracker(modifier = modifier, model = model)
    }
}

@Composable
private fun DefaultStepTracker(modifier: Modifier, model: StepTrackerModel) {
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        Box(
            modifier = Modifier
                .height(8.dp)
                .weight(1f)
                .background(
                    color = IkonnectedColors.Tertiary.PowderBlue,
                    shape = RoundedCornerShape(4.dp)
                )
        ) {
            Box(
                modifier = Modifier
                    .height(8.dp)
                    .fillMaxWidth(model.currentStep / model.totalSteps.toFloat())
                    .background(
                        color = IkonnectedColors.Primary.Blue,
                        shape = RoundedCornerShape(4.dp)
                    )
            )
        }
    }
}

@Composable
private fun GappedStepTracker(modifier: Modifier, model: StepTrackerModel) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(Theme.spacing.x2)
    ) {
        repeat(model.totalSteps) { index ->
            val stepNumber = index + 1
            val isCompleted = stepNumber <= model.currentStep

            Box(
                modifier = Modifier
                    .height(8.dp)
                    .weight(1f)
                    .background(
                        color = if (isCompleted) {
                            IkonnectedColors.Primary.Blue
                        } else {
                            IkonnectedColors.Primary.Blue.copy(alpha = 0.1f)
                        },
                        shape = RoundedCornerShape(4.dp)
                    )
            )
        }
    }
}


