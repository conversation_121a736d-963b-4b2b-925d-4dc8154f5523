package com.ikonnected.compose.ui.theme

import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.ikonnected.compose.ui.theme.internal.ClauseStyleColors
import ikonnected.compose_ui.generated.resources.NunitoSans_Bold
import ikonnected.compose_ui.generated.resources.NunitoSans_Regular
import ikonnected.compose_ui.generated.resources.Res
import org.jetbrains.compose.resources.Font


@Composable
fun IkonnectedTheme(
    invertedColors: Boolean = isSystemInDarkTheme(),
    content: @Composable () -> Unit,
) {

    val ikonnectedColor = Colors(
        surface = Surface(
            primaryDark = IkonnectedColors.Blue,
            primaryLight = IkonnectedColors.White,
            secondaryDark = IkonnectedColors.Black,
            secondaryLight = IkonnectedColors.White,
        ),
        surfaceInteractive = SurfaceInteractive(
            enabledOnDark = IkonnectedColors.White,
            enabledOnLight = IkonnectedColors.Black,
            disabledOnDark = IkonnectedColors.Secondary.NeutralSix,
            disabledOnLight = IkonnectedColors.Secondary.NeutralSix,
            hoverTapOnDark = IkonnectedColors.Secondary.NeutralFour,
            hoverTapOnLight = IkonnectedColors.Secondary.NeutralFour
        ),
        border = Border(
            primaryOnDark = IkonnectedColors.Border.Default,
            primaryOnLight = IkonnectedColors.Border.Default,
            secondaryOnDark = IkonnectedColors.Secondary.NeutralSix,
            secondaryOnLight = IkonnectedColors.Secondary.NeutralSix,
        ),
        element = Element(
            primaryOnDark = IkonnectedColors.White,
            primaryOnLight = IkonnectedColors.Black,
            secondaryOnDark = IkonnectedColors.White,
            secondaryOnLight = IkonnectedColors.Black,
        ),
        elementInteractive = ElementInteractive(
            enabledOnDark = IkonnectedColors.White,
            enabledOnLight = IkonnectedColors.Primary.Blue,
            disabledOnDark = IkonnectedColors.White.copy(alpha = 0.1f),
            disabledOnLight = IkonnectedColors.Secondary.NeutralSix,
            hoverTapOnDark = IkonnectedColors.White.copy(alpha = 0.6f),
            hoverTapOnLight = IkonnectedColors.Primary.Blue.copy(alpha = 0.8f)
        ),
        shadow = Shadow(
            spotOnDark = IkonnectedColors.Shadow.lightSpot,
            spotOnLight = IkonnectedColors.Shadow.darkSpot
        ),
    )

    val interFontFamily = FontFamily(
        Font(Res.font.NunitoSans_Regular, FontWeight.Normal),
        Font(Res.font.NunitoSans_Regular, FontWeight.Medium),
        Font(Res.font.NunitoSans_Bold, FontWeight.Bold)
    )

    val typography = Typography(
        headingSub = TextStyle(
            fontFamily = interFontFamily,
            fontWeight = FontWeight.Normal,
            fontSize = 12.sp,
            color = Color(0xFF525252)
        ),
        headingS = TextStyle(
            fontFamily = interFontFamily,
            fontWeight = FontWeight.Medium,
            fontSize = 16.sp,
            color = ikonnectedColor.element.primaryOnLight
        ),
        headingM = TextStyle(
            fontFamily = interFontFamily,
            fontWeight = FontWeight.SemiBold,
            fontSize = 18.sp,
            color = ikonnectedColor.element.primaryOnLight
        ),
        headingL = TextStyle(
            fontFamily = interFontFamily,
            fontWeight = FontWeight.Normal,
            fontSize = 24.sp,
            color = ikonnectedColor.element.primaryOnLight
        ),
        bodyXs = TextStyle(
            fontFamily = interFontFamily,
            fontWeight = FontWeight.Normal,
            fontSize = 12.sp,
            color = ikonnectedColor.element.primaryOnLight
        ),
        bodyS = TextStyle(
            fontFamily = interFontFamily,
            fontWeight = FontWeight.Normal,
            fontSize = 14.sp,
            lineHeight = 20.sp,
            color = ikonnectedColor.element.primaryOnLight
        ),
        body = TextStyle(
            fontFamily = interFontFamily,
            fontWeight = FontWeight.Normal,
            fontSize = 16.sp,
            color = ikonnectedColor.element.primaryOnLight,
        ),
        labelXs = TextStyle(
            fontFamily = interFontFamily,
            fontWeight = FontWeight.Medium,
            fontSize = 10.sp,
            color = ikonnectedColor.element.primaryOnLight
        )
    )


    val shape = Shape(
        buttonShape = RoundedCornerShape(8.dp),
        circleShape = RoundedCornerShape(50),
        roundShape = RoundedCornerShape(12.dp),
        imageShape = CircleShape,
        card = RoundedCornerShape(8.dp),
        textShape = RoundedCornerShape(4.dp),
        chipShape = RoundedCornerShape(8.dp),
    )
    
    WithTheme(
        colors = ikonnectedColor,
        typography = typography,
        content = content,
        shape = shape,
        clauseStyleColors = provideClauseColors(invertedColors),
    )

}

/**
 * Enforce the light mode
 */
@Composable
fun isSystemInDarkTheme() = false


@Composable
private fun provideClauseColors(invertedColors: Boolean) = when (invertedColors) {
    true -> ClauseStyleColors(
        regular = IkonnectedColors.Secondary.NeutralOne,
        caution = IkonnectedColors.Secondary.NeutralFour,
        success = IkonnectedColors.Primary.Black,
        error = IkonnectedColors.Destructive
    )

    false -> ClauseStyleColors(
        regular = IkonnectedColors.Secondary.NeutralOne,
        caution = IkonnectedColors.Secondary.NeutralFour,
        success = IkonnectedColors.Primary.Black,
        error = IkonnectedColors.Destructive
    )
}