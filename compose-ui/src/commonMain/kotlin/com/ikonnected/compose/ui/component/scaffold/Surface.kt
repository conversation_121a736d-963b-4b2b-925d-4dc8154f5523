package com.ikonnected.compose.ui.component.scaffold

import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.Shape
import com.ikonnected.compose.ui.theme.Theme
import com.ikonnected.compose.ui.theme.isSystemInDarkTheme


@Composable
internal fun Surface(
    modifier: Modifier = Modifier,
    color: Color? = null,
    shape: Shape = RectangleShape,
    invertedColors: Boolean = isSystemInDarkTheme(),
    content: @Composable () -> Unit,
) {

    Surface(
        modifier = modifier,
        color = color ?: backgroundColor(invertedColors),
        shape = shape
    ) {
        content()
    }
}


@Composable
private fun backgroundColor(invertedColors: Boolean): Color {
    return when (invertedColors) {
        true -> Theme.colors.surface.primaryDark
        false -> Theme.colors.surface.primaryLight
    }
}