package com.ikonnected.compose.ui.navigation.model.internal

import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.ui.graphics.Color


@Immutable
internal data class BottomBarColors(
    val activeIconColor: Color = Color.Unspecified,
    val inactiveIconColor: Color = Color.Unspecified,
    val pressedColor: Color = Color.Unspecified
)

internal val LocalBottomBarColorProvider = compositionLocalOf { BottomBarColors() }

@Composable
internal fun ProvideBottomBarColors(
    colors: BottomBarColors,
    content: @Composable () -> Unit
) = CompositionLocalProvider(LocalBottomBarColorProvider provides colors, content = content)