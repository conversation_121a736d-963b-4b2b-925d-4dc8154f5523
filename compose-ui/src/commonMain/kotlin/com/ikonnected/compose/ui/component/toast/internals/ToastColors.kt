package com.ikonnected.compose.ui.component.toast.internals

import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.ui.graphics.Color

@Immutable
internal data class ToastColors(
    val backgroundColor: Color = Color.Unspecified,
    val titleColor: Color = Color.Unspecified,
    val subtitleColor: Color = Color.Unspecified,
    val iconColor: Color? = null
)

internal val LocalToastColorProvider = compositionLocalOf { ToastColors() }

@Composable
internal fun ProvideToastColors(
    colors: ToastColors,
    content: @Composable () -> Unit
) = CompositionLocalProvider(LocalToastColorProvider provides colors, content = content)

