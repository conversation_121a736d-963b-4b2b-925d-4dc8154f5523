package com.ikonnected.compose.ui.component.field

import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.ikonnected.compose.ui.component.field.model.TextInputModel

@Composable
fun LabelTextArea(
    model: TextInputModel,
    minLine: Int = 4,
    maxLine: Int = Int.MAX_VALUE,
    isBorderLess: Boolean = false,
    modifier: Modifier = Modifier,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions(),
    onValueChange: (String) -> Unit
) {
    LabelTextField(
        model = model,
        singleLine = false,
        minLine = minLine,
        maxLine = maxLine,
        isBorderLess = isBorderLess,
        modifier = modifier,
        keyboardOptions = keyboardOptions,
        keyboardActions = keyboardActions,
        onValueChange = onValueChange
    )
}