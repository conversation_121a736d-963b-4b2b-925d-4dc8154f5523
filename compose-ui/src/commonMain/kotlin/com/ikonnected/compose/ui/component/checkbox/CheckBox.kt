package com.ikonnected.compose.ui.component.checkbox

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.width
import androidx.compose.material.Checkbox
import androidx.compose.material.CheckboxDefaults
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.LocalMinimumInteractiveComponentEnforcement
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.ikonnected.compose.ui.component.checkbox.model.CheckBoxModel
import com.ikonnected.compose.ui.element.text.Text
import com.ikonnected.compose.ui.theme.IkonnectedColors
import com.ikonnected.compose.ui.theme.Theme

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun CheckBox(
    model: CheckBoxModel,
    modifier: Modifier = Modifier,
    onCheckedChanged: (Boolean) -> Unit
) {
    var isChecked by remember(model.isChecked) { mutableStateOf(model.isChecked) }

    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        CompositionLocalProvider(LocalMinimumInteractiveComponentEnforcement provides false) {
            Checkbox(
                checked = isChecked,
                colors = CheckboxDefaults.colors(
                    checkedColor = IkonnectedColors.Primary.Blue,
                    uncheckedColor = Color(0xFFD5DAE5),
                    checkmarkColor = IkonnectedColors.White
                ),
                onCheckedChange = {
                    isChecked = it
                    onCheckedChanged.invoke(isChecked)
                }
            )
        }
        Spacer(modifier = Modifier.width(Theme.spacing.x2))
        Text(clause = model.title)
    }
}