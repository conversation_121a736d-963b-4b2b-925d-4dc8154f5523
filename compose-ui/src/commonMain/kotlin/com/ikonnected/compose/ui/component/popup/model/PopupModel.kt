package com.ikonnected.compose.ui.component.popup.model

import androidx.compose.runtime.Immutable
import com.ikonnected.compose.ui.component.button.model.ButtonModel
import com.ikonnected.compose.ui.element.image.Image
import com.ikonnected.compose.ui.element.text.Clause


@Immutable
data class PopupModel(
    val headerImage: Image? = null,
    val title: Clause,
    val subtitle: Clause,
    val dismissible: Boolean = true,
    val primaryBtn: ButtonModel? = null,
    val secondaryBtn: ButtonModel? = null,
)

enum class PopupEventType {
    Primary, Secondary, Dismiss
}
