package com.ikonnected.compose.ui.element.brush

import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.ikonnected.compose.ui.theme.IkonnectedColors


fun Modifier.dotted(cornerRadius: Dp = 8.dp, stroke: Dp = 1.dp) = drawBehind {
    val pathEffect = PathEffect.dashPathEffect(
        floatArrayOf(10f, 10f),
        0f
    )
    drawRoundRect(
        color = IkonnectedColors.Primary.LightGray,
        style = Stroke(
            width = stroke.toPx(),
            pathEffect = pathEffect
        ),
        cornerRadius = CornerRadius(cornerRadius.toPx(), cornerRadius.toPx())
    )
}