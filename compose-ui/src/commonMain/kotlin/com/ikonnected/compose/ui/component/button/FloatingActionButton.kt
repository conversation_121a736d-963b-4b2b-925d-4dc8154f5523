package com.ikonnected.compose.ui.component.button

import androidx.compose.foundation.layout.size
import androidx.compose.material.FloatingActionButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.ikonnected.compose.ui.element.image.Image
import com.ikonnected.compose.ui.theme.IkonnectedColors


@Composable
fun FloatingActionButton(
    image: Image,
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {

    FloatingActionButton(
        onClick = onClick,
        modifier = modifier,
        backgroundColor = IkonnectedColors.Destructive
    ) {
        Image(image = image, modifier = Modifier.size(22.dp))
    }

}