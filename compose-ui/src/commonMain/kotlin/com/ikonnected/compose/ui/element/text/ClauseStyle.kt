package com.ikonnected.compose.ui.element.text

import androidx.compose.runtime.Composable
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import com.ikonnected.compose.ui.element.text.ClauseStyle.*
import com.ikonnected.compose.ui.theme.IkonnectedColors
import com.ikonnected.compose.ui.theme.internal.ClauseStyleColors

enum class ClauseStyle {
    Regular, Caution, Emphasis, Success, Error;
}

@Composable
internal fun ClauseStyle.toColor() = when (this) {
    Regular, Emphasis -> LocalClauseStyleProvider.current.regular
    Caution -> LocalClauseStyleProvider.current.caution
    Success -> LocalClauseStyleProvider.current.success
    Error -> LocalClauseStyleProvider.current.error
}

internal val LocalClauseStyleProvider = compositionLocalOf { ClauseStyleColors() }

@Composable
operator fun SpanStyle.plus(style: ClauseStyle?): SpanStyle {
    if (style == null) return this
    if (style == ClauseStyle.Emphasis){
        // Experimental -> increase the font weight by 100 for emphasis
        return this.copy(
            fontWeight = FontWeight(fontWeight!!.weight + 100),
            color = IkonnectedColors.Primary.Black
        )
    }
    return this.copy(color = style.toColor())
}

@Composable
operator fun TextStyle.plus(style: ClauseStyle?): TextStyle {
    val color = style?.toColor() ?: return this
    return this.copy(color = color)
}
