package com.ikonnected.compose.ui.element.image

import ikonnected.compose_ui.generated.resources.Res
import ikonnected.compose_ui.generated.resources.avatar
import ikonnected.compose_ui.generated.resources.img_locked
import ikonnected.compose_ui.generated.resources.img_mail
import ikonnected.compose_ui.generated.resources.img_unlocked

object Images {
    val avatar = Image.Drawable(Res.drawable.avatar)
    val locked = Image.Drawable(Res.drawable.img_locked)
    val unlocked = Image.Drawable(Res.drawable.img_unlocked)
    val mail = Image.Drawable(Res.drawable.img_mail)
}