package com.ikonnected.compose.ui.element.image

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.DefaultAlpha
import androidx.compose.ui.layout.ContentScale
import com.ikonnected.compose.ui.theme.Theme
import org.jetbrains.compose.resources.DrawableResource

@Immutable
sealed class Image {

    @Immutable
    data class Drawable(
        val resId: DrawableResource
    ) : Image()

    @Immutable
    data class Bitmap(
        val filePath: String,
        val placeholder: Drawable? = null,
    ) : Image()
}

internal fun Image.placeholder() = when(this) {
    is Image.Bitmap -> this.placeholder?.resId
    else -> null
}

@Composable
fun Icon(
    image: Image,
    modifier: Modifier,
    contentDescription: String? = null,
    adaptive: Boolean = false,
    contentScale: ContentScale = ContentScale.Fit,
    alpha: Float = DefaultAlpha,
    colorFilter: ColorFilter? = null,
    onClick: OptionalButtonCallback = null,
    invertedColors: Boolean = isSystemInDarkTheme(),
) {

    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()

    val iconColorFilter = colorFilter ?: ColorFilter.tint(
        color = when (invertedColors) {
            true -> Theme.colors.element.primaryOnDark
            false -> Theme.colors.element.primaryOnLight
        }
    ).takeIf { adaptive }

    Image(
        image = image,
        modifier = if (onClick == null) modifier else modifier.clickable(
            interactionSource = interactionSource,
            indication = null,
            enabled = true,
            onClick = {
                onClick.invoke()
            }
        ),
        contentDescription = contentDescription,
        contentScale = contentScale,
        alpha = alpha,
        colorFilter = iconColorFilter
    )
}

@Composable
fun Image(
    image: Image,
    modifier: Modifier,
    contentDescription: String? = null,
    contentScale: ContentScale = ContentScale.Fit,
    alpha: Float = DefaultAlpha,
    colorFilter: ColorFilter? = null,
) {
    Image(
        painter = painterImage(
            image,
            image.placeholder()
        ),
        modifier = modifier,
        contentScale = contentScale,
        contentDescription = contentDescription,
        alpha = alpha,
        colorFilter = colorFilter,
    )
}


typealias OptionalButtonCallback = (() -> Unit)?

fun OptionalButtonCallback.isEnabled(): Boolean {
    return this != null
}