package com.ikonnected.compose.ui.element.text

import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import com.ikonnected.compose.ui.element.text.internal.toText
import com.ikonnected.compose.ui.theme.IkonnectedColors
import com.ikonnected.compose.ui.theme.Theme


/**
 * This currently only supports Text Clause.
 * Try to find the best way to have or use native
 */
@Composable
fun TextLink(
    clause: Text,
    modifier: Modifier = Modifier,
    pressedColor: Color = IkonnectedColors.Secondary.NeutralFive,
    textDecoration: TextDecoration = TextDecoration.Underline,
    style: TextStyle = Theme.typography.body.copy(fontWeight = FontWeight.SemiBold),
    enabled: Boolean = true,
    onClick: () -> Unit
) {

    val textColor = clause.clauseTextStyle?.toColor() ?: style.color

    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()

    androidx.compose.material.Text(
        text = clause.toText(),
        style = style.copy(
            textDecoration = textDecoration,
            color = if (isPressed) pressedColor else textColor
        ),
        modifier = modifier.clickable(
            interactionSource = interactionSource,
            indication = null,
            enabled = enabled,
            onClick = onClick
        ),
    )
}