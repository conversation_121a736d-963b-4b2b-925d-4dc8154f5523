package com.ikonnected.compose.ui.component.switch

import androidx.compose.material3.Switch
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.ikonnected.compose.ui.component.switch.internals.ProvideSwitchColors
import com.ikonnected.compose.ui.component.switch.internals.SwitchColors
import com.ikonnected.compose.ui.component.switch.internals.LocalSwitchColorProvider
import com.ikonnected.compose.ui.component.switch.internals.toSwitchColors
import com.ikonnected.compose.ui.component.switch.model.SwitchModel
import com.ikonnected.compose.ui.theme.Theme
import com.ikonnected.compose.ui.theme.isSystemInDarkTheme


@Composable
fun Switch(
    model: SwitchModel,
    onCheckedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    invertedColors: Boolean = isSystemInDarkTheme(),
) {
    ProvideSwitchColors(colors = componentColors(invertedColors = invertedColors)) {
        Switch(
            checked = model.isChecked,
            onCheckedChange = onCheckedChange,
            modifier = modifier,
            enabled = model.enabled,
            colors = LocalSwitchColorProvider.current.toSwitchColors()
        )
    }
}

@Composable
private fun componentColors(invertedColors: Boolean): SwitchColors = when (invertedColors) {
    true -> SwitchColors(
        checkedThumbColor = Theme.colors.element.secondaryOnLight,
        checkedTrackColor = Theme.colors.elementInteractive.enabledOnLight,
    )
    false -> SwitchColors(
        checkedThumbColor = Theme.colors.element.primaryOnDark,
        checkedTrackColor = Theme.colors.elementInteractive.enabledOnLight,
    )
}