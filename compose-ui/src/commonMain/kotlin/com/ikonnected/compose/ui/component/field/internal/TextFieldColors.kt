package com.ikonnected.compose.ui.component.field.internal

import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.ui.graphics.Color
import com.ikonnected.compose.ui.theme.IkonnectedColors

internal data class TextFieldColors(
    val labelColor: Color = Color.Unspecified,
    val placeholderColor: Color = Color.Unspecified,
    val cursorColor: Color = Color.Unspecified,
    val errorColor: Color = Color.Unspecified,
    val footerTextColor: Color = Color.Unspecified,
    val focusedBorderColor: Color = Color.Unspecified,
    val unfocusedBorderColor: Color = Color.Unspecified,
    val unfocusedBorderLessColor: Color = Color.Unspecified,
    val focusedIndicatorColor: Color = Color.Unspecified,
    val unfocusedIndicatorColor: Color = Color.Unspecified,
    val selectionBackgroundColor: Color = Color.Unspecified,
    val leadingContentColor: Color = Color.Unspecified,
    val unfocusedLeadingContentColor: Color = Color.Unspecified,
    val trailingContentColor: Color = Color.Unspecified,
    val unfocusedTrailingContentColor: Color = Color.Unspecified,
    val verticalDividerLineColor: Color = Color.Unspecified
)

internal val LocalTextFieldColorsProvider = compositionLocalOf { TextFieldColors() }

@Composable
internal fun ProvideTextFieldColors(
    colors: TextFieldColors,
    content: @Composable () -> Unit,
) = CompositionLocalProvider(LocalTextFieldColorsProvider provides colors, content = content)

internal fun componentColors(invertedColors: Boolean) = when(invertedColors) {
true -> TextFieldColors(
        labelColor = IkonnectedColors.Black,
        placeholderColor = IkonnectedColors.Secondary.NeutralFive,
        cursorColor = IkonnectedColors.Black,
        errorColor = IkonnectedColors.Destructive,
        footerTextColor = IkonnectedColors.Destructive,
        selectionBackgroundColor = IkonnectedColors.Secondary.NeutralFive,
        focusedIndicatorColor = IkonnectedColors.Black,
        unfocusedIndicatorColor = IkonnectedColors.Secondary.NeutralFive,
        unfocusedBorderLessColor = IkonnectedColors.Primary.LightGray
    )
    false -> TextFieldColors(
        labelColor = IkonnectedColors.Black,
        placeholderColor = IkonnectedColors.Secondary.NeutralFive,
        cursorColor = IkonnectedColors.Black,
        errorColor = IkonnectedColors.Destructive,
        selectionBackgroundColor = IkonnectedColors.Secondary.NeutralSix,
        footerTextColor = IkonnectedColors.Destructive,
        focusedIndicatorColor = IkonnectedColors.Black,
        unfocusedIndicatorColor = IkonnectedColors.Input,
        unfocusedBorderLessColor = IkonnectedColors.Primary.LightGray
    )
}
