package com.ikonnected.compose.ui.component.divider

import androidx.compose.material.Divider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.ikonnected.compose.ui.theme.Theme
import com.ikonnected.compose.ui.theme.isSystemInDarkTheme

@Composable
fun Divider(
    color: Color? = null,
    modifier: Modifier = Modifier,
    thickness: Dp = 1.dp,
    invertedColors: Boolean = isSystemInDarkTheme(),
) {
    Divider(
        color = color ?: componentColors(invertedColors),
        thickness = thickness,
        modifier = modifier
    )
}

@Composable
private fun componentColors(invertedColors: Boolean) = when (invertedColors) {
    true -> Theme.colors.surfaceInteractive.enabledOnDark
    false -> Theme.colors.surfaceInteractive.enabledOnLight
}