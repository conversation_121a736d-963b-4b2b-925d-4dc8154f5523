package com.ikonnected.compose.ui.component.contentbox

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.ikonnected.compose.ui.component.contentbox.model.ErrorBoxModel
import com.ikonnected.compose.ui.element.image.Image
import com.ikonnected.compose.ui.element.text.Text
import com.ikonnected.compose.ui.theme.IkonnectedColors
import com.ikonnected.compose.ui.theme.Theme

@Composable
fun ErrorBox(
    model: ErrorBoxModel,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.border(
            width = 1.dp,
            color = Color(0xFFFECACA),
            shape = RoundedCornerShape(8.dp)
        ).padding(
            horizontal = Theme.spacing.x4,
            vertical = Theme.spacing.x3
        )
    ) {
        Image(image = model.leadingIcon, modifier = Modifier.size(16.dp))
        Spacer(modifier = Modifier.width(Theme.spacing.x3))
        Column(modifier = Modifier.weight(1f)) {
            Text(
                clause = model.title,
                style = Theme.typography.headingM.copy(color = IkonnectedColors.Destructive)
            )
            Spacer(modifier = Modifier.height(Theme.spacing.x1))
            Text(
                clause = model.subtitle,
                style = Theme.typography.body.copy(color = IkonnectedColors.Destructive)
            )
        }

    }

}