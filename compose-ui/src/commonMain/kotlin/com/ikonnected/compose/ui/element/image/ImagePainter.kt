package com.ikonnected.compose.ui.element.image

import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.graphics.painter.Painter
import coil3.compose.LocalPlatformContext
import coil3.compose.rememberAsyncImagePainter
import coil3.request.CachePolicy
import coil3.request.ImageRequest
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource


@Composable
fun WithImagePainter(
    imagePainterFactory: @Composable (String, DrawableResource?) -> Painter,
    content: @Composable () -> Unit
) = CompositionLocalProvider(
    LocalBitmapPainterFactory provides imagePainterFactory,
    content = content
)

object ImagePainterFactory {

    @Composable
    fun createPainterFactory(): @Composable (String, DrawableResource?) -> Painter =
        { imageUrl, placeholderResId ->
            rememberAsyncImagePainter(
                model = ImageRequest
                    .Builder(LocalPlatformContext.current)
                    .data(imageUrl)
                    .placeholder(image = placeholderResId?.let {
                        // TODO -> Add the placeholder
                        null
                    })
//                    .placeholder(
//                        drawable = placeholderResId?.let {
//                            AppCompatResources.getDrawable(LocalContext.current, it)
//                        }
//                    )
                    .memoryCachePolicy(policy = CachePolicy.ENABLED)
                    .listener(onError = { a, error ->
                        println("Image could not be loaded")
                        error.throwable.printStackTrace()
                    })
                    .build()
            )
        }
}

@Composable
internal fun painterImage(
    image: Image,
    placeholderResId: DrawableResource? = null,
): Painter {
    return when (image) {
        is Image.Drawable -> painterResource(image.resId)
        is Image.Bitmap -> {
            val bitmapFactory = LocalBitmapPainterFactory.current
            bitmapFactory!!.invoke(image.filePath, placeholderResId)
        }
    }
}

private val LocalBitmapPainterFactory =
    staticCompositionLocalOf<(@Composable (String, DrawableResource?) -> Painter)?> { null }