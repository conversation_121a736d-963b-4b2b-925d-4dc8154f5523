package com.ikonnected.compose.ui.component.scaffold

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.material.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.ikonnected.compose.ui.theme.Theme
import com.ikonnected.compose.ui.theme.isSystemInDarkTheme

@Composable
fun Scaffold(
    modifier: Modifier = Modifier,
    contentWindowInsets: WindowInsets = WindowInsets(0),
    backgroundColor: Color? = null,
    invertedColors: Boolean = isSystemInDarkTheme(),
    topBar: @Composable () -> Unit = {},
    bottomBar: @Composable () -> Unit = {},
    floatingActionBar: @Composable () -> Unit = {},
    content: @Composable (PaddingValues) -> Unit,
) {

    Scaffold(
        modifier = modifier,
        contentWindowInsets = contentWindowInsets,
        topBar = topBar,
        bottomBar = bottomBar,
        floatingActionButton = floatingActionBar,
        backgroundColor = backgroundColor ?: backgroundColor(invertedColors),
        content = content
    )
}

@Composable
private fun backgroundColor(invertedColors: Boolean) : Color {
    return when(invertedColors) {
        true -> Theme.colors.surface.primaryDark
        false -> Theme.colors.surface.primaryLight
    }
}