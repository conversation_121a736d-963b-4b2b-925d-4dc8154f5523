package com.ikonnected.compose.ui.component.field.model

import com.ikonnected.compose.ui.element.image.Image
import com.ikonnected.compose.ui.element.text.Clause
import com.ikonnected.compose.ui.element.text.Text

data class TextInputModel(
    val label: Clause? = null,
    val value: String,
    val placeholder: Clause,
    val isError: Boolean = false,
    val footerText: Clause? = null,
    val leadingIcon: Image? = null,
    val trailingIcon: Image? = null,
) {

    companion object {
        val Default = TextInputModel(
            label = Text.empty,
            value = "",
            placeholder = Text.empty,
            isError = false,
            footerText = null,
        )
    }

    operator fun plus(value: String): TextInputModel {
        return this.copy(
            value = value
        )
    }

}
