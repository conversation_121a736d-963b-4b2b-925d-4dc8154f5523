package com.ikonnected.compose.ui.component.button

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.ikonnected.compose.ui.component.button.internals.ButtonColors
import com.ikonnected.compose.ui.component.button.internals.ProvideButtonColors
import com.ikonnected.compose.ui.component.button.model.ButtonModel
import com.ikonnected.compose.ui.component.button.model.ButtonState
import com.ikonnected.compose.ui.component.button.model.ButtonStyle
import com.ikonnected.compose.ui.element.image.OptionalButtonCallback
import com.ikonnected.compose.ui.theme.IkonnectedColors
import com.ikonnected.compose.ui.theme.isSystemInDarkTheme

@Composable
fun DangerButton(
    model: ButtonModel,
    modifier: Modifier = Modifier,
    state: ButtonState = ButtonState.Enabled,
    style: ButtonStyle = ButtonStyle.Regular,
    onClick: OptionalButtonCallback,
    invertedColors: Boolean = isSystemInDarkTheme(),
) {
    ProvideButtonColors(colors = componentColors(invertedColors = invertedColors)) {
        BaseButton(
            model = model,
            modifier = modifier,
            state = state,
            style = style,
            onClick = onClick,
        )
    }
}

@Composable
private fun componentColors(invertedColors: Boolean) = when (invertedColors) {
    true -> ButtonColors(
        backgroundColor = IkonnectedColors.Destructive,
        contentColor = IkonnectedColors.White,
    )

    false -> ButtonColors(
        backgroundColor = IkonnectedColors.Destructive,
        contentColor = IkonnectedColors.White,
    )
}