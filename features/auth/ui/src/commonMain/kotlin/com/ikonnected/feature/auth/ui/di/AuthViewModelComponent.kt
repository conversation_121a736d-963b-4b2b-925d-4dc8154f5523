package com.ikonnected.feature.auth.ui.di

import com.ikonnected.feature.auth.ui.navigation.ViewModelProviders
import com.ikonnected.feature.auth.ui.screens.dispatch.DispatchViewModel
import com.ikonnected.feature.auth.ui.screens.dispatch.di.DispatchUiComponent
import me.tatarka.inject.annotations.Component


@Component
internal abstract class AuthViewModelComponent(
    private val dispatchUiComponent: DispatchUiComponent,
) : ViewModelProviders {

    override val dispatchViewModel: () -> DispatchViewModel
        get() = { dispatchUiComponent.viewModel }

}
