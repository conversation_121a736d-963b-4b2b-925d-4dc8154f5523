package com.ikonnected.feature.auth.ui.navigation

import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavGraphBuilder
import androidx.navigation.navigation
import com.ikonnected.core.presentation.navigation.NavigationDispatcher
import com.ikonnected.core.presentation.navigation.compose.NavigatorHost
import com.ikonnected.core.presentation.navigation.compose.composable
import com.ikonnected.feature.auth.ui.launcher.AuthFeatureLauncher
import com.ikonnected.feature.auth.ui.screens.dispatch.DispatchScreen
import me.tatarka.inject.annotations.Inject


internal class AuthNavigationHost @Inject constructor(
    private val viewModelProviders: ViewModelProviders,
) : NavigatorHost.Child {

    override fun buildGraph(
        builder: NavGraphBuilder,
        navigationDispatcher: NavigationDispatcher
    ) {
        builder.navigation(route = ROOT_ROUTE, startDestination = "landing") {

            composable(route = "landing") { backStackEntry ->
                DispatchScreen(
                    viewModel = viewModel { viewModelProviders.dispatchViewModel() },
                    navigationDispatcher = navigationDispatcher,
                )
            }


        }
    }

    companion object {
        private const val ROOT_ROUTE = AuthFeatureLauncher.FEATURE_NAME
        private val loginScreenPath = screenPath("login")
        private val pickOptionsScreenPath = screenPath("pickOptions")

        private fun screenPath(name: String) = "$ROOT_ROUTE/$name"
    }

}