package com.ikonnected.feature.auth.ui.screens.dispatch

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.windowInsetsBottomHeight
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.ikonnected.compose.ui.component.scaffold.Scaffold
import com.ikonnected.compose.ui.theme.Theme
import com.ikonnected.core.presentation.navigation.NavigationDispatcher
import com.ikonnected.feature.auth.ui.screens.dispatch.DispatchContract.Intent

@Composable
internal fun DispatchScreen(
    viewModel: DispatchViewModel,
    navigationDispatcher: NavigationDispatcher
) {

    val viewState by remember { derivedStateOf { viewModel.state.value } }

    viewState.navigationCommand?.let {
        navigationDispatcher.navigateTo(it)
        viewModel.emit(Intent.Navigation.Done)
    }

    Scaffold(contentWindowInsets = WindowInsets.statusBars) { paddingValues ->
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Theme.spacing.x2),
            modifier = Modifier.padding(paddingValues).padding(horizontal = Theme.spacing.x4)
        ) {

            Spacer(Modifier.windowInsetsBottomHeight(WindowInsets.navigationBars))
        }

    }

}