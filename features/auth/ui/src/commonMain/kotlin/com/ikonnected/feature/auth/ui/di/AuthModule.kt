package com.ikonnected.feature.auth.ui.di

import com.ikonnected.core.presentation.feature.FeatureLauncher
import com.ikonnected.feature.auth.ui.launcher.AuthFeatureLauncher
import com.ikonnected.feature.auth.ui.screens.dispatch.di.DispatchUiComponent
import com.ikonnected.feature.auth.ui.screens.dispatch.di.create
import me.tatarka.inject.annotations.Component
import me.tatarka.inject.annotations.IntoMap
import me.tatarka.inject.annotations.Provides

@Component
abstract class AuthModule {
    abstract val features: Map<String, FeatureLauncher>

    @Provides
    @IntoMap
    protected fun provideFeatureLauncher(launcher: FeatureLauncher): Pair<String, FeatureLauncher> =
        AuthFeatureLauncher.FEATURE_NAME to launcher


    @Provides
    internal fun provideAuthFeatureLauncher(
    ): FeatureLauncher = AuthFeatureLauncher(
        viewModelProviders = AuthViewModelComponent::class.create(
            dispatchUiComponent = DispatchUiComponent::class.create(),
        ),
    )

}
