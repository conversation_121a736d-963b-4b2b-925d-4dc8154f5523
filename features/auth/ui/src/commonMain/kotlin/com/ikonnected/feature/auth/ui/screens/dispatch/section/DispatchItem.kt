package com.ikonnected.feature.auth.ui.screens.dispatch.section

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.ikonnected.compose.ui.element.image.Image
import com.ikonnected.compose.ui.element.text.Text
import com.ikonnected.compose.ui.theme.Theme
import com.ikonnected.feature.auth.ui.screens.dispatch.model.DispatchModel


@Composable
internal fun DispatchItem(
    modifier: Modifier = Modifier,
    item: DispatchModel,
) {
    Column(modifier = modifier.padding(Theme.spacing.x2)) {
        Image(
            image = item.image,
            contentScale = ContentScale.Inside,
            modifier = Modifier
                .size(450.dp)
        )
        Spacer(modifier = Modifier.height(Theme.spacing.x1))
        Text(
            clause = item.title,
            style = Theme.typography.headingM.copy(
                color = Theme.colors.element.primaryOnDark
            )
        )
        Spacer(modifier = Modifier.height(Theme.spacing.x2))
        Text(
            clause = item.subtitle,
            style = Theme.typography.bodyS.copy(
                textAlign = TextAlign.Center,
                color = Theme.colors.element.primaryOnDark
            )
        )
    }
}