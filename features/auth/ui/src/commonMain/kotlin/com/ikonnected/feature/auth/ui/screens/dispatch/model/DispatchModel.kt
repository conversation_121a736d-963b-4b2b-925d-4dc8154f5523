package com.ikonnected.feature.auth.ui.screens.dispatch.model

import com.ikonnected.compose.ui.element.image.Image
import com.ikonnected.compose.ui.element.image.Images
import com.ikonnected.compose.ui.element.text.Clause
import com.ikonnected.compose.ui.element.text.asText

internal data class DispatchModel(
    val title: Clause,
    val subtitle: Clause,
    val image: Image
) {

    companion object{
        val connect = DispatchModel(
            title = "Always Connected Anywhere Anytime".asText(),
            subtitle = "By using the Ikonnected platform you can always be connected anywhere anytime.".asText(),
            image = Images.avatar
        )

        val experience = DispatchModel(
            title = "Enjoy New Experiences With New Friends".asText(),
            subtitle = "Enjoy your surfing experience on the Ikonnected with your new friends from all over the world.".asText(),
            image = Images.avatar
        )

        val discover = DispatchModel(
            title = "Discover Interesting Things Every Day".asText(),
            subtitle = "You can get new and interesting things every day even every second when you start scrolling.".asText(),
            image = Images.avatar
        )
    }
}