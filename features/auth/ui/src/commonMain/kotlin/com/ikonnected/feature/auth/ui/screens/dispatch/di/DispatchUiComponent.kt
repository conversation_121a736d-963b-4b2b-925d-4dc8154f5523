package com.ikonnected.feature.auth.ui.screens.dispatch.di

import com.ikonnected.core.presentation.mvi.Reducer
import com.ikonnected.core.presentation.mvi.Interactor
import com.ikonnected.feature.auth.ui.screens.dispatch.DispatchViewModel
import com.ikonnected.feature.auth.ui.screens.dispatch.DispatchContract.Intent
import com.ikonnected.feature.auth.ui.screens.dispatch.DispatchContract.ViewState
import com.ikonnected.feature.auth.ui.screens.dispatch.DispatchContract.Result
import com.ikonnected.feature.auth.ui.screens.dispatch.DispatchInteractor
import com.ikonnected.feature.auth.ui.screens.dispatch.DispatchMapper
import com.ikonnected.feature.auth.ui.screens.dispatch.DispatchReducer
import me.tatarka.inject.annotations.Component
import me.tatarka.inject.annotations.Provides

@Component
internal abstract class DispatchUiComponent {
    abstract val viewModel: DispatchViewModel

    @Provides
    fun provideViewState(): ViewState = ViewState.Default

    @Provides
    fun provideReducer(): Reducer<ViewState, Result> = DispatchReducer()

    @Provides
    fun provideInteractor(): Interactor<Intent, Result> = DispatchInteractor(
        DispatchMapper()
    )

}