package com.ikonnected.feature.auth.ui.screens.dispatch

import com.ikonnected.core.presentation.mvi.Reducer
import com.ikonnected.feature.auth.ui.screens.dispatch.DispatchContract.Result
import com.ikonnected.feature.auth.ui.screens.dispatch.DispatchContract.ViewState
import me.tatarka.inject.annotations.Inject

internal class DispatchReducer @Inject constructor() : Reducer<ViewState, Result> {
    override fun reduce(state: ViewState, result: Result): ViewState {
        return when (result) {
            is Result.OnNavigationCommandUpdated -> state.copy(navigationCommand = result.navigationCommand)
            is Result.DispatchModelUpdated -> state.copy(items = result.items)
            is Result.OnGetStartedBtnStateUpdated -> state.copy(getStartedState = result.buttonState)
            is Result.OnDataLoaded -> state.copy(items = result.items)
            is Result.OnSkipButtonStatedUpdated -> state.copy(
                skipBtnState = result.buttonState,
                stepTrackerModel = result.stepTrackerModel
            )
            is Result.OnStepTrackerUpdated -> state.copy(stepTrackerModel = result.stepTrackerModel)
        }
    }
}