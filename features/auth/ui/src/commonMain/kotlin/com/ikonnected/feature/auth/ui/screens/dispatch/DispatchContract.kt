package com.ikonnected.feature.auth.ui.screens.dispatch

import androidx.compose.runtime.Immutable
import com.ikonnected.compose.ui.component.button.model.ButtonModel
import com.ikonnected.compose.ui.component.button.model.ButtonState
import com.ikonnected.compose.ui.component.steptracker.model.StepTrackerModel
import com.ikonnected.compose.ui.element.text.asText
import com.ikonnected.core.presentation.navigation.NavigationCommand
import com.ikonnected.feature.auth.ui.screens.dispatch.model.DispatchModel

internal object DispatchContract {

    interface Mapper {
        fun toResult(intent: Intent.LoadData): Result.OnDataLoaded
        fun toResult(intent: Intent.Navigation): Result.OnNavigationCommandUpdated
        fun toResult(intent: Intent.OnSkipButtonClick): Result.OnSkipButtonStatedUpdated
        fun toResult(intent: Intent.OnGetStartButtonClick): Result.OnNavigationCommandUpdated
        fun toResult(intent: Intent.SignInTextActionClick): Result.OnNavigationCommandUpdated
    }

    sealed class Intent {
        data object LoadData : Intent()
        data object OnGetStartButtonClick : Intent()
        data class SignInTextActionClick(val actionId: String) : Intent()
        data object OnSkipButtonClick : Intent()
        sealed class Navigation : Intent() {
            data object Done : Navigation()
        }

    }

    sealed class Result {
        data class OnNavigationCommandUpdated(val navigationCommand: NavigationCommand?) : Result()
        data class OnDataLoaded(val items: List<DispatchModel>) : Result()
        data class OnSkipButtonStatedUpdated(
            val buttonState: ButtonState,
            val stepTrackerModel: StepTrackerModel
        ) : Result()

        data class OnStepTrackerUpdated(val stepTrackerModel: StepTrackerModel) : Result()
        data class OnGetStartedBtnStateUpdated(val buttonState: ButtonState) : Result()
        data class DispatchModelUpdated(val items: List<DispatchModel>) : Result()
    }

    @Immutable
    data class ViewState(
        val navigationCommand: NavigationCommand?,
        val items: List<DispatchModel>,
        val getStartedBtnModel: ButtonModel,
        val getStartedState: ButtonState,
        val skipBtnModel: ButtonModel,
        val skipBtnState: ButtonState,
        val stepTrackerModel: StepTrackerModel,
        val footerClause: String,
    ) {
        companion object {
            val Default = ViewState(
                navigationCommand = null,
                items = listOf(),
                skipBtnModel = ButtonModel(
                    "Skip".asText(),
                ),
                skipBtnState = ButtonState.Enabled,
                getStartedBtnModel = ButtonModel(
                    "Get Started".asText(),
                ),
                getStartedState = ButtonState.Enabled,
                stepTrackerModel = StepTrackerModel(
                    totalSteps = 3,
                    currentStep = 0,
                ),
                footerClause = "Sign in",
            )
        }

    }

}