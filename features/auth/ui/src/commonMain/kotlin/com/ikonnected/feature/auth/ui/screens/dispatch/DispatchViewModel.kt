package com.ikonnected.feature.auth.ui.screens.dispatch

import com.ikonnected.core.presentation.mvi.FlowViewModelArgs
import com.ikonnected.core.presentation.mvi.Interactor
import com.ikonnected.core.presentation.mvi.LifecycleViewModel
import com.ikonnected.core.presentation.mvi.Reducer
import com.ikonnected.feature.auth.ui.screens.dispatch.DispatchContract.Intent
import com.ikonnected.feature.auth.ui.screens.dispatch.DispatchContract.Result
import com.ikonnected.feature.auth.ui.screens.dispatch.DispatchContract.ViewState
import me.tatarka.inject.annotations.Inject

internal class DispatchViewModel @Inject constructor(
    initialState: ViewState,
    interactor: Interactor<Intent, Result>,
    reducer: Reducer<ViewState, Result>
) : LifecycleViewModel<Intent, Result, ViewState>(
    args = FlowViewModelArgs(
        initialState = initialState,
        reducer = reducer,
        interactor = interactor
    )
)
