package com.ikonnected.feature.auth.ui.screens.dispatch

import me.tatarka.inject.annotations.Inject
import com.ikonnected.feature.auth.ui.screens.dispatch.DispatchContract.Intent
import com.ikonnected.feature.auth.ui.screens.dispatch.DispatchContract.Result
import com.ikonnected.feature.auth.ui.screens.dispatch.DispatchContract.Mapper

internal class DispatchMapper @Inject constructor() : Mapper {
    override fun toResult(intent: Intent.LoadData): Result.OnDataLoaded {
        TODO("Not yet implemented")
    }

    override fun toResult(intent: Intent.Navigation): Result.OnNavigationCommandUpdated {
        return when (intent) {
            Intent.Navigation.Done -> Result.OnNavigationCommandUpdated(null)
        }
    }

    override fun toResult(intent: Intent.OnSkipButtonClick): Result.OnSkipButtonStatedUpdated {
        TODO("Not yet implemented")
    }

    override fun toResult(intent: Intent.OnGetStartButtonClick): Result.OnNavigationCommandUpdated {
        TODO("Not yet implemented")
    }

    override fun toResult(intent: Intent.SignInTextActionClick): Result.OnNavigationCommandUpdated {
        TODO("Not yet implemented")
    }
}