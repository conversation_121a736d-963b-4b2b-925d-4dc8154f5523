package com.ikonnected.feature.auth.ui.launcher

import com.ikonnected.core.presentation.feature.FeatureLauncher
import com.ikonnected.core.presentation.navigation.compose.NavigatorHost
import com.ikonnected.feature.auth.ui.navigation.AuthNavigationHost
import com.ikonnected.feature.auth.ui.navigation.ViewModelProviders
import me.tatarka.inject.annotations.Inject

internal class AuthFeatureLauncher @Inject constructor(
    private val viewModelProviders: ViewModelProviders,
) : FeatureLauncher {

    override val featureName: String = FEATURE_NAME

    override fun navigationGraph(): NavigatorHost.Child {
        return AuthNavigationHost(viewModelProviders)
    }

    companion object {
        internal const val FEATURE_NAME = "auth"
    }

}