import UIKit
import SwiftUI
import ComposeApp
import FirebaseCrashlytics

struct ComposeView: UIViewControllerRepresentable {
    func makeUIViewController(context: Context) -> UIViewController {
        MainViewControllerKt.MainViewController()
    }

    func updateUIViewController(_ uiViewController: UIViewController, context: Context) {}
}

struct ContentView: View {
    var body: some View {
        VStack {
            ComposeView()
                .ignoresSafeArea(.keyboard) // Compose has own keyboard handler

            // Test crash button - remove this in production
            #if DEBUG
            <PERSON>("Test Crash") {
                Crashlytics.crashlytics().log("Test crash button tapped")
                fatalError("Test crash for Crashlytics")
            }
            .padding()
            .background(Color.red)
            .foregroundColor(.white)
            .cornerRadius(8)
            #endif
        }
    }
}



