package com.ikonnected.app.di


import com.ikonnected.app.AppContext
import com.ikonnected.app.getContext
import com.ikonnected.database.data.IkonnectedDatabaseManager
import com.ikonnected.domain.auth.data.datasource.local.DatabaseInitializationListener
import me.tatarka.inject.annotations.Inject

internal class DatabaseInitializationListenerImpl @Inject constructor(
    private val appContext: AppContext,
    private val dbNameProvider: () -> String,
) : DatabaseInitializationListener {

    override fun open(password: String) {
        IkonnectedDatabaseManager.initialize(
            appContext = appContext.getContext(),
            "${dbNameProvider.invoke()}.db",
            password = password.encodeToByteArray()
        )
    }
}