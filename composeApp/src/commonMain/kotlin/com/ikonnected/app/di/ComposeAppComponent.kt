package com.ikonnected.app.di

import com.ikonnected.core.presentation.feature.FeatureLauncher
import com.ikonnected.core.presentation.navigation.compose.NavigatorHost
import com.ikonnected.app.navigation.MainNavigatorHost
import com.ikonnected.core.discopes.SingletonScope
import com.ikonnected.feature.auth.ui.di.AuthModule
import me.tatarka.inject.annotations.Component
import me.tatarka.inject.annotations.Provides


@SingletonScope
@Component
internal abstract class ComposeAppComponent(
    private val authModule: AuthModule,
) {
    abstract val root: NavigatorHost.Root

    protected val MainNavigatorHost.bind: NavigatorHost.Root
        @Provides get() = this

    @Provides
    fun provideFeatures(): Map<String, FeatureLauncher> = authModule.features
}

