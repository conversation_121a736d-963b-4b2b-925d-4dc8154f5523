package com.ikonnected.app.di

import com.ikonnected.core.auth.OauthTokenProvider
import com.ikonnected.core.discopes.SingletonScope
import com.ikonnected.core.network.CoreClient
import com.ikonnected.core.network.CoreClientImpl
import me.tatarka.inject.annotations.Component
import me.tatarka.inject.annotations.Provides

@SingletonScope
@Component
internal abstract class NetworkComponent(
    private val tokenProvider: OauthTokenProvider,
    private val baseUrl: String
) {
    abstract val client: CoreClient

    @SingletonScope
    @Provides
    fun provideCoreClient(): CoreClient =
        CoreClientImpl(tokenProvider, baseUrl)
}
