package com.ikonnected.app.ui

import com.ikonnected.core.presentation.mvi.Reducer
import com.ikonnected.app.ui.MainContract.ViewState
import com.ikonnected.app.ui.MainContract.Result

internal class MainReducer constructor() : Reducer<ViewState, Result> {
    override fun reduce(state: ViewState, result: Result): ViewState {
        return when (result) {
            Result.AlertDismissed -> TODO()
            Result.ClearNav -> TODO()
            is Result.Initial -> state.copy(startDestinationRoute = result.startDestinationRoute)
            is Result.BottomNavigationUpdated -> state.copy(
                navCommand = result.navigationCommand,
                bottomModel = result.bottomModel
            )
            is Result.NavigationCommandUpdated -> state.copy(navCommand = result.navCommand)
            is Result.RouteChanged -> state.copy(
                currentRoute = result.route,
                bottomState = result.bottomBarState,
                pageTransition = result.pageTransition
            )
        }
    }
}