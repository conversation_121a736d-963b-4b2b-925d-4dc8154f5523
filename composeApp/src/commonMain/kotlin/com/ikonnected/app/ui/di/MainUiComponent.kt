package com.ikonnected.app.ui.di

import com.ikonnected.app.ui.MainContract.Intent
import com.ikonnected.app.ui.MainContract.Result
import com.ikonnected.app.ui.MainContract.ViewState
import com.ikonnected.app.ui.MainInteractor
import com.ikonnected.app.ui.MainMapper
import com.ikonnected.app.ui.MainReducer
import com.ikonnected.app.ui.MainViewModel
import com.ikonnected.core.presentation.mvi.Interactor
import com.ikonnected.core.presentation.mvi.Reducer
import me.tatarka.inject.annotations.Component
import me.tatarka.inject.annotations.Provides

@Component
internal abstract class MainUiComponent {
    abstract val viewModel: MainViewModel

    @Provides
    protected fun provideDefaultState(): ViewState = ViewState.Default

    @Provides
    protected fun provideInteractor(): Interactor<Intent, Result> =
        MainInteractor(mapper = MainMapper())

    @Provides
    fun provideReducer(): Reducer<ViewState, Result> = MainReducer()
}