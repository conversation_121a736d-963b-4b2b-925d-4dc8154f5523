package com.ikonnected.app.ui

import com.ikonnected.core.presentation.mvi.FlowViewModelArgs
import com.ikonnected.core.presentation.mvi.Interactor
import com.ikonnected.core.presentation.mvi.LifecycleViewModel
import com.ikonnected.core.presentation.mvi.Reducer
import me.tatarka.inject.annotations.Inject

@Inject
internal class MainViewModel constructor(
    initialState: MainContract.ViewState,
    interactor: Interactor<MainContract.Intent, MainContract.Result>,
    reducer: Reducer<MainContract.ViewState, MainContract.Result>
) : LifecycleViewModel<MainContract.Intent, MainContract.Result, MainContract.ViewState>(
    args = FlowViewModelArgs(
        initialState = initialState,
        reducer = reducer,
        interactor = interactor
    )
)
