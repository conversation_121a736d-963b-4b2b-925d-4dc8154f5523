package com.ikonnected.app.navigation

import androidx.navigation.NavController
import androidx.navigation.NavOptionsBuilder
import com.ikonnected.core.presentation.navigation.NavigationCommand
import com.ikonnected.core.presentation.navigation.NavigationDispatcher
import me.tatarka.inject.annotations.Inject


internal class NavigationNotSupportedException(message: String) : Exception(message)

internal class MainNavigationDispatcher @Inject constructor(
    private val navController: NavController,
    private val builderProducer: NavOptionsBuilderProducer = NavOptionsBuilderProducer(navController = navController)
) : NavigationDispatcher {

    override fun navigateTo(command: NavigationCommand) {
        when (command) {
            is NavigationCommand.FeatureCommand -> {
                when(command.clearStack) {
                    true -> navController.navigate(
                        route = command.featureName,
                        builder = builderProducer::navOptionsBuilder
                    )
                    false -> navController.navigate(route = command.featureName)
                }
            }
            else -> throw NavigationNotSupportedException(message = "navigation to $command is not supported")
        }
    }

}

internal class NavOptionsBuilderProducer(
    private val navController: NavController
) {

    fun navOptionsBuilder(builder: NavOptionsBuilder) {
        var popResult = navController.popBackStack()
        while (popResult) {
            popResult = navController.popBackStack()
        }
    }

}