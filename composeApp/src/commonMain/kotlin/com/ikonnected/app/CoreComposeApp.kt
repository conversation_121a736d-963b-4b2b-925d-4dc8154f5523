package com.ikonnected.app

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.lifecycle.viewmodel.compose.viewModel
import com.ikonnected.app.ui.MainViewModel
import com.ikonnected.compose.ui.component.toast.ToastData
import com.ikonnected.compose.ui.component.toast.ToastItem
import com.ikonnected.compose.ui.component.toast.WithToastHandler
import com.ikonnected.compose.ui.element.image.ImagePainterFactory
import com.ikonnected.compose.ui.element.image.WithImagePainter
import com.ikonnected.compose.ui.theme.IkonnectedTheme
import com.ikonnected.compose.ui.theme.Theme
import com.ikonnected.core.presentation.navigation.compose.NavigatorHost

@Composable
internal fun CoreComposeApp(
    root: NavigatorHost.Root,
    mainViewModelProvider: () -> MainViewModel
) {

    var activeToast by remember { mutableStateOf<ToastData?>(null) }

    WithImagePainter(imagePainterFactory = ImagePainterFactory.createPainterFactory()) {
        IkonnectedTheme {
            WithToastHandler(
                interact = { toast ->
                    activeToast = toast
                }
            ) {
                Box(contentAlignment = Alignment.TopCenter, modifier = Modifier.fillMaxSize()) {
                    RootScreen(
                        viewModel = viewModel { mainViewModelProvider.invoke() },
                        navigatorHost = root
                    )
                    activeToast?.let { toast ->
                        ToastItem(
                            model = toast,
                            modifier = Modifier
                                .align(Alignment.Center)
                                .padding(top = Theme.spacing.x14)
                                .padding(
                                    top = WindowInsets.systemBars.asPaddingValues().calculateTopPadding(),
                                    start = Theme.spacing.x3,
                                    end = Theme.spacing.x3
                                ).wrapContentSize(),
                            onDismissed = { activeToast = null }
                        )
                    }
                }
            }
        }
    }
}