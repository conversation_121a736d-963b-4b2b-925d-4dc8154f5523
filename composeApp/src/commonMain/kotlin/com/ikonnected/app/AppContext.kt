package com.ikonnected.app

import com.ikonnected.core.log.ErrorLogger
import com.ikonnected.core.network.CoreFileUploadConverter
import com.ikonnected.core.sharedpref.AppSharedStorageProvider


internal expect class AppContext constructor(any: Any)

internal expect fun AppContext.buildSharedStorage(storageFileName: String): AppSharedStorageProvider
internal expect fun AppContext.buildErrorLogger(): ErrorLogger
internal expect fun AppContext.getContext(): Any
internal expect fun AppContext.buildFileConverter(): CoreFileUploadConverter