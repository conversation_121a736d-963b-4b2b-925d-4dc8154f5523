package com.ikonnected.app.storage

import android.content.Context
import com.ikonnected.core.sharedpref.AppSharedStorageProvider

internal class CoreSharedStorage(context: Context, prefName: String) : AppSharedStorageProvider {

    private val sharedPreferences = context.getSharedPreferences(prefName, Context.MODE_PRIVATE)

    override fun get(key: String): String? {
        return sharedPreferences.getString(key, null)
    }

    override fun set(key: String, value: String): Boolean {
        return sharedPreferences.edit().apply {
            putString(key, value)
        }.commit()
    }

    override fun clear(key: String): <PERSON><PERSON><PERSON> {
        return sharedPreferences.edit().apply {
            remove(key)
        }.commit()
    }
}

