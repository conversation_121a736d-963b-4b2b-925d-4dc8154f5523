package com.ikonnected.app

import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.fragment.app.FragmentActivity
import com.ikonnected.app.di.RootAppBuilder
import com.ikonnected.core.file.FilePicker
import com.ikonnected.core.permissions.PermissionsManager

class MainActivity : FragmentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val appContext = AppContext(applicationContext)
        val app = RootAppBuilder.build(appContext, this,"https://api.sampleapis.com/")

        //TODO: Setup activity monitoring to handle activity lifecycle events

        enableEdgeToEdge()

        setContent {
            CoreComposeApp(
                root = app.first,
                mainViewModelProvider = { app.second }
            )
        }

        // Initialize the permission manager
        // TODO - Check for memory leaks
        PermissionsManager.initialize(this)

        // Initialize the file picker
        FilePicker.initialize(this)
    }
}

