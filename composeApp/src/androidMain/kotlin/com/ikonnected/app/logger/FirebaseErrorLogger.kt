package com.ikonnected.app.logger

import android.util.Log
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.ikonnected.core.log.ErrorLogger

internal class FirebaseErrorLogger constructor(
    private val crashlytics: FirebaseCrashlytics
) : <PERSON><PERSON><PERSON><PERSON>ogger {
    override fun log(message: String) {
        crashlytics.log(message)
    }

    override fun log(exception: Exception) {
        if (exception is IllegalStateException) {
            // IllegalStateException is often used for expected errors, so we log it differently
            exception.localizedMessage?.let { Log.e("Ikonnected error", it) }
        } else {
            crashlytics.recordException(exception)
        }
    }

    override fun log(throwable: Throwable) {
        throwable.localizedMessage?.let { Log.e("Ikonnected error", it) }
        crashlytics.recordException(throwable)
    }
}