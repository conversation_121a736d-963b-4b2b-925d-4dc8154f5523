package com.ikonnected.app.logger

import android.util.Log
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.ikonnected.core.log.ErrorLogger

internal class FirebaseErrorLogger constructor(
    private val crashlytics : FirebaseCrashlytics
) : <PERSON><PERSON><PERSON><PERSON>ogger {
    override fun log(message: String) {
        crashlytics.log(message)
    }

    override fun log(exception: Exception) {
        if (true) {
            exception.localizedMessage?.let { Log.e("Ikonnected error", it) }
        } else {
            crashlytics.recordException(exception)
        }
    }

    override fun log(throwable: Throwable) {
        if (true) {
            throwable.localizedMessage?.let { Log.e("Ikonnected error", it) }
        } else {
            crashlytics.recordException(throwable)
        }
    }
}