package com.ikonnected.app

import android.content.Context
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.ikonnected.app.logger.FirebaseErrorLogger
import com.ikonnected.app.storage.CoreSharedStorage
import com.ikonnected.core.log.ErrorLogger
import com.ikonnected.core.network.CoreFileUploadConverter
import com.ikonnected.core.sharedpref.AppSharedStorageProvider

internal actual class AppContext actual constructor(any: Any) {
    var context: Context
        private set

    init {
        context = any as Context
    }
}

internal actual fun AppContext.getContext(): Any {
    return context
}

internal actual fun AppContext.buildSharedStorage(storageFileName: String): AppSharedStorageProvider {
    return CoreSharedStorage(context = context, storageFileName)
}

internal actual fun AppContext.buildErrorLogger(): ErrorLogger {
    return FirebaseErrorLogger(FirebaseCrashlytics.getInstance())
}

internal actual fun AppContext.buildFileConverter(): CoreFileUploadConverter {
    return FileUploaderImpl(appContext = context)
}
