package com.ikonnected.app.storage

import com.ikonnected.core.sharedpref.AppSharedStorageProvider
import platform.Foundation.NSUserDefaults
import platform.Foundation.setValue

internal class CoreSharedStorage constructor(
    private val userDefaults: NSUserDefaults = NSUserDefaults.standardUserDefaults()
) : AppSharedStorageProvider {


    override fun get(key: String): String? {
        return userDefaults.stringForKey(key)
    }

    override fun set(key: String, value: String): Boolean {
        userDefaults.setValue(value = value, forKey = key)
        return true
    }

    override fun clear(key: String): Boolean {
        return when {
            get(key = key) != null -> userDefaults.removeObjectForKey(key).let { true }
            else -> false
        }
    }
}