package com.ikonnected.app

import com.ikonnected.app.storage.CoreSharedStorage
import com.ikonnected.core.log.ErrorLogger
import com.ikonnected.core.network.CoreFileUploadConverter
import com.ikonnected.core.sharedpref.AppSharedStorageProvider
import platform.Foundation.NSLog

internal actual class AppContext actual constructor(any: Any)

internal actual fun AppContext.getContext(): Any {
    return "context"
}

internal actual fun AppContext.buildSharedStorage(storageFileName: String): AppSharedStorageProvider {
    return CoreSharedStorage()
}

internal actual fun AppContext.buildErrorLogger(): ErrorLogger {

    return object : ErrorLogger {
        override fun log(message: String) {
            NSLog("Ikonnected error %s", message)
        }

        override fun log(exception: Exception) {
            NSLog("Ikonnected error %s", exception.message)
        }

        override fun log(throwable: Throwable) {

        }
    }
}

internal actual fun AppContext.buildFileConverter(): CoreFileUploadConverter {
    return object : CoreFileUploadConverter {
        override fun execute(filePath: String): ByteArray? {
            // TODO -> Provide the implementation
            return null
        }

        override fun audioFile(filePath: String): ByteArray? {
            return null
        }
    }
}