package com.ikonnected.core.presentation.navigation


sealed class NavigationCommand {
    data class ScreenCommand(
        val route: String,
        val clearStack: Boolean = false,
        val arguments: Map<String, NavigationArgument> = emptyMap()
    ) : NavigationCommand()

    data class CloseFeature(
        val result: Map<String, NavigationArgument> = emptyMap()
    ) : NavigationCommand()

    data class FeatureCommand(
        val featureName: String,
        val clearStack: Boolean = false,
        val arguments: Map<String, NavigationArgument> = emptyMap()
    ) : NavigationCommand()

    data class BackCommand(val result: Map<String, NavigationArgument> = emptyMap()) :
        NavigationCommand()

    data class ExternalUriCommand(
        val uri: String
    ): NavigationCommand()

}

sealed class NavigationArgument {
    data class StringArgument(val value: String) : NavigationArgument()
    data class LongArgument(val value: Long) : NavigationArgument()
    data class IntArgument(val value: Int) : NavigationArgument()
    data class FloatArgument(val value: Float) : NavigationArgument()
    data class BooleanArgument(val value: Boolean) : NavigationArgument()
    data class ParcelableArgument(val value: MyParcelable) : NavigationArgument()
}