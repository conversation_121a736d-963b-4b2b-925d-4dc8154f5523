package com.ikonnected.core.presentation.mvi

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob

class FlowViewModelArgs<Intent, Result, State> private constructor(
    override val initialState: State,
    private val interactor: Interactor<Intent, Result>,
    private val reducer: Reducer<State, Result>,
    private val intentFlowHolder: IntentFlowHolder<Intent>,
    private val coroutineScope: CoroutineScope
) : LifecycleViewModel.ViewModelArgs<Intent, Result, State>() {

    constructor(
        initialState: State,
        interactor: Interactor<Intent, Result>,
        reducer: Reducer<State, Result>,
    ) : this(
        initialState = initialState,
        interactor = interactor,
        reducer = reducer,
        intentFlowHolder = SharedIntentFlowHolder(),
        coroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.Main.immediate)
    )

    override fun buildMviProcessor(): MviProcessor<Intent, Result, State> = FlowMviProcessor(
        initialState = initialState,
        interactor = interactor,
        reducer = reducer,
        intentFlowHolder = intentFlowHolder,
        coroutineScope = coroutineScope
    )
}