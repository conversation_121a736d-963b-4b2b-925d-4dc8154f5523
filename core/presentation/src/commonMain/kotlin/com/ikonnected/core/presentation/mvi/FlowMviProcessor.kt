package com.ikonnected.core.presentation.mvi

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.scan
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

class FlowMviProcessor<Intent, Result, State>(
    private val interactor: Interactor<Intent, Result>,
    private val reducer: Reducer<State, Result>,
    private val intentFlowHolder: IntentFlowHolder<Intent>,
    private val coroutineScope: CoroutineScope,
    private val initialState: State
) : MviProcessor<Intent, Result, State> {

    private val viewState: StateFlow<State>
        get() = intentFlowHolder.intents
            .let(interactor::process)
            .scan(initialState, reducer::reduce)
            .stateIn(
                scope = coroutineScope,
                started = SharingStarted.Eagerly,
                initialValue = initialState
            )

    override fun bind(observer: (State) -> Unit) {
        viewState
            .onEach(observer)
            .launchIn(coroutineScope)
    }

    override fun dispose() {
        coroutineScope.cancel()
    }

    override fun emit(intent: Intent) {
        coroutineScope.launch {
            intentFlowHolder.emit(intent)
        }
    }
}