package com.ikonnected.core.datetime

import kotlin.time.Instant
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.format.FormatStringsInDatetimeFormats
import kotlinx.datetime.format.byUnicodePattern
import kotlinx.datetime.toInstant
import kotlinx.datetime.toLocalDateTime
import kotlin.time.ExperimentalTime

@OptIn(ExperimentalTime::class)
interface DateTimeParser {
    fun parse(date: String, timeZone: TimeZone? = null): Long
    fun format(
        time: Long,
        format: String? = null,
        timeZone: TimeZone? = null,
    ): String

    companion object {
        fun create(): DateTimeParser = DateTimeParserImpl()
    }
}

@OptIn(FormatStringsInDatetimeFormats::class)
internal class DateTimeParserImpl : DateTimeParser {

    @OptIn(ExperimentalTime::class)
    override fun format(time: Long, format: String?, timeZone: TimeZone?): String {
        val dateTimeFormat = LocalDateTime.Format {
            byUnicodePattern(format ?: DEFAULT_SERVER_FORMAT)
        }
        val localDateTime =
            Instant.fromEpochMilliseconds(time).toLocalDateTime(timeZone ?: SERVER_TIME_ZONE)
        return dateTimeFormat.format(localDateTime)
    }

    @OptIn(ExperimentalTime::class)
    override fun parse(date: String, timeZone: TimeZone?): Long {
        val dateTimeFormat = LocalDateTime.Format {
            byUnicodePattern(DEFAULT_SERVER_FORMAT)
        }
        return dateTimeFormat.parse(date)
            .toInstant(timeZone = timeZone ?: TimeZone.currentSystemDefault()).toEpochMilliseconds()
    }

    companion object {
        const val DEFAULT_SERVER_FORMAT = ("yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'")
        val SERVER_TIME_ZONE: TimeZone = TimeZone.of("UTC")
    }
}