package com.ikonnected.core.auth

import com.ikonnected.core.sharedpref.AppSharedStorageProvider
import kotlinx.serialization.json.Json
import me.tatarka.inject.annotations.Inject

@Inject
interface OauthTokenProvider {
    fun getToken(): IkonnectedAuthToken?
    fun setToken(token: IkonnectedAuthToken)

    fun setEmail(email: String)
    fun getEmail(): String?
    fun clearToken()

    companion object {
        fun create(sharedPef: AppSharedStorageProvider): OauthTokenProvider =
            OauthTokenProviderImpl(sharedPef = sharedPef)
    }
}

internal class OauthTokenProviderImpl @Inject constructor(
    private val sharedPef: AppSharedStorageProvider
) : OauthTokenProvider {

    override fun getToken(): IkonnectedAuthToken? {
        return sharedPef.get(OAUTH_TOKEN_KEY)?.let(Json::decodeFromString)
    }

    override fun setToken(token: IkonnectedAuthToken) {
        val jsonString = Json.encodeToString(token)
        sharedPef.set(OAUTH_TOKEN_KEY, jsonString)
    }


    override fun setEmail(email: String) {
        sharedPef.set(EMAIL_KEY, email)
    }

    override fun getEmail(): String? {
        return sharedPef.get(EMAIL_KEY)
    }

    override fun clearToken() {
        sharedPef.clear(OAUTH_TOKEN_KEY)
        sharedPef.clear(EMAIL_KEY)
    }

    companion object {
        private const val OAUTH_TOKEN_KEY = "oauth_token"

        private const val EMAIL_KEY = "email"
    }

}