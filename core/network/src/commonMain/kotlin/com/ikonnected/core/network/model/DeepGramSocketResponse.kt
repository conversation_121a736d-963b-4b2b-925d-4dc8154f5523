package com.ikonnected.core.network.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class DeepGramResult(
    @SerialName(value = "type")
    val type: String,
    @SerialName(value = "duration")
    val duration: Double,
    @SerialName(value = "start")
    val start: Double,
    @SerialName(value = "is_final")
    val isFinal: <PERSON><PERSON><PERSON>,
    @SerialName(value = "channel")
    val channel: DeepGramChannel,
)

@Serializable
data class DeepGramChannel(
    @SerialName(value = "alternatives")
    val alternatives: List<DeepGramAlternatives>,
)

@Serializable
data class DeepGramAlternatives(
    @SerialName(value = "transcript")
    val transcript: String,
    @SerialName(value = "confidence")
    val confidence: Double,
    @SerialName(value = "words")
    val words: List<DeepGramAlternativesWord>,
)

@Serializable
data class DeepGramAlternativesWord(
    @SerialName(value = "word")
    val word: String,
    @SerialName(value = "start")
    val start: Double,
    @SerialName(value = "end")
    val end: Double,
    @SerialName(value = "confidence")
    val confidence: Double,
    @SerialName(value = "speaker")
    val speaker: Int,
)
