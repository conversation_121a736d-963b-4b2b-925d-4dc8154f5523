package com.ikonnected.core.network

import co.touchlab.kermit.Logger
import com.ikonnected.core.auth.OauthTokenProvider
import com.ikonnected.core.discopes.SingletonScope
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.plugins.ClientRequestException
import io.ktor.client.plugins.HttpResponseValidator
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.plugins.defaultRequest
import io.ktor.client.plugins.logging.LogLevel
import io.ktor.client.plugins.logging.Logging
import io.ktor.client.request.header
import io.ktor.serialization.kotlinx.json.json
import kotlinx.serialization.json.Json
import me.tatarka.inject.annotations.Inject

interface CoreClient {
    val httpClient: HttpClient
}

@SingletonScope
class CoreClientImpl @Inject constructor(
    private val tokenProvider: OauthTokenProvider,
    private val baseUrl: String
) : CoreClient {

    override val httpClient: HttpClient
        get() = HttpClient {
            // default validation to throw exceptions for non-2xx responses
            expectSuccess = true

            // set default request parameters
            defaultRequest {
                // add base url for all request
                url(baseUrl) //sets scheme + host + full path prefix

                tokenProvider.getToken()?.let { token ->
                    Logger.e { "Toss $token" }
                    header(AUTHORIZATION_TOKEN, "Bearer $token")
                }
            }

            // use gson content negotiation for serialize or deserialize
            install(ContentNegotiation) {
                json(Json {
                    ignoreUnknownKeys = true
                    useAlternativeNames = false
                })
            }

            install(Logging) {
                logger = object : io.ktor.client.plugins.logging.Logger {
                    override fun log(message: String) {
                        Logger.d { message }
                    }
                }
                level = LogLevel.INFO
            }

            HttpResponseValidator {
                handleResponseExceptionWithRequest { exception, request ->
                    val clientException = exception as? ClientRequestException
                        ?: return@handleResponseExceptionWithRequest
                    val error: CoreRequestError = clientException.response.body()
                    print("HTTP Error occurred ${error.message}")
                    throw error.asException()
                }
            }
        }

    companion object {
        const val AUTHORIZATION_TOKEN = "Authorization"
    }

}
