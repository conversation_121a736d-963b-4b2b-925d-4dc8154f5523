package com.ikonnected.core.network

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class GeneralResponse<T>(
    @SerialName(value = "data")
    val data: T,
    @SerialName(value = "meta")
    val meta: MetaData? = null,
    @SerialName(value = "nextPageToken")
    val nextPageToken: String? = null
)

@Serializable
data class OkApiResponse(
    @SerialName(value = "message")
    val message: String? = null,
)

@Serializable
data class MetaData(
    @SerialName(value = "current_page")
    val currentPage: Int,
    @SerialName(value = "next_page")
    val nextPage: Int? = null,
    @SerialName(value = "per_page")
    val perPage: Int,
    @SerialName(value = "previous_page")
    val previousPage: Int? = null,
    @SerialName(value = "total_pages")
    val totalPages: Int,
    @SerialName(value = "total_results")
    val totalResults: Int
)