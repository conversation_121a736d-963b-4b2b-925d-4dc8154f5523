plugins {
    alias(libs.plugins.kotlinMultiplatform)
    alias(libs.plugins.kotlinxSerialization)
    alias(libs.plugins.ksp)
}

kotlin {
    jvm()
    listOf(
        iosX64(),
        iosArm64(),
        iosSimulatorArm64()
    ).forEach { iosTarget ->
        iosTarget.binaries.framework {
            baseName = "CoreNetwork"
            isStatic = true
        }
    }
    sourceSets {
        jvmMain.dependencies {
            implementation(libs.ktor.client.okhttp)
        }
        commonMain.dependencies {
            implementation(libs.coroutine.core)
            implementation(libs.ktor.client.core)
            implementation(libs.ktor.client.cio)
            implementation(libs.ktor.client.websockets)
            implementation(libs.ktor.content.negotiation)
            implementation(libs.ktor.client.logging)

            implementation(libs.kermit.logger)

            api(libs.ktor.serialization.kotlinx.json)

            implementation(libs.kotlin.inject.runtime.kmp)
            implementation(projects.core.auth)
            api(projects.core.discopes)
            api(projects.core.log)
        }
    }
}

dependencies {
    add("commonMainImplementation", libs.ktor.client.core)
    add("kspCommonMainMetadata", libs.kotlin.inject.compiler)
}
