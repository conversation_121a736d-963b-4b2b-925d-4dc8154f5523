package com.ikonnected.core.file

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import kotlin.coroutines.CoroutineContext

interface FilePicker {
    fun execute(request: FilePickerRequest): Flow<FilePickerResult>

    companion object {
        fun build(appContext: Any) = buildFilePicker(appContext = appContext)

        lateinit var shared : FilePicker
        fun initialize(appContext: Any) {
            this.shared = buildFilePicker(
                appContext = appContext
            )
        }
    }

}

/**
 * Here is an utility function that returns a single result
 */
suspend fun FilePicker.execute(
    request: FilePickerRequest,
    dispatcher: CoroutineContext = Dispatchers.Unconfined
): FilePickerResult {
    return withContext(dispatcher) {
        execute(request).first()
    }
}

expect fun buildFilePicker(appContext: Any): FilePicker