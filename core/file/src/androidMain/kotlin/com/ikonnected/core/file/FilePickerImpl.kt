package com.ikonnected.core.file

import android.net.Uri
import android.os.Environment
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.FileProvider
import androidx.fragment.app.FragmentActivity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

internal class FilePickerImpl constructor(
    private val activity: FragmentActivity,
    private val coroutineScope: CoroutineScope = CoroutineScope(Dispatchers.Unconfined + SupervisorJob())
) : FilePicker {

    private val _sharedFlow = MutableSharedFlow<FilePickerResult>()
    private lateinit var lastFileUri: Uri

    private val requestLauncher =
        activity.registerForActivityResult(
            ActivityResultContracts.PickMultipleVisualMedia()
        ) { uris ->
            if (uris.isNotEmpty()) {
                coroutineScope.launch {
                    val paths = uris.map { requireNotNull(it.toString()) }
                    _sharedFlow.emit(FilePickerResult.Success(paths))
                }
            } else {
                coroutineScope.launch {
                    _sharedFlow.emit(FilePickerResult.Failure)
                }
            }
        }

    private val takePictureLauncher =
        activity.registerForActivityResult(ActivityResultContracts.TakePicture()) { success: Boolean ->
            if (success) {
                coroutineScope.launch {
                    _sharedFlow.emit(FilePickerResult.Success(listOf(lastFileUri.toString())))
                }
            } else {
                coroutineScope.launch {
                    _sharedFlow.emit(FilePickerResult.Failure)
                }
            }
        }

    override fun execute(request: FilePickerRequest): Flow<FilePickerResult> {
        when (request) {
            is FilePickerRequest.Capture -> {
                lastFileUri = createFile()
                takePictureLauncher.launch(lastFileUri)
            }
            is FilePickerRequest.Media -> requestLauncher.launch(
                PickVisualMediaRequest(
                    ActivityResultContracts.PickVisualMedia.ImageOnly
                )
            )
        }
        return _sharedFlow
    }

    private fun createFile(): Uri {
        val timeStamp: String =
            SimpleDateFormat(FILENAME_FORMAT, Locale.getDefault()).format(Date())
        val storageDir: File = activity.getExternalFilesDir(Environment.DIRECTORY_PICTURES)!!
        val file = File.createTempFile(
            "JPEG_${timeStamp}_", ".jpg", storageDir
        )
        return FileProvider.getUriForFile(activity, "com.ikonnected.app.provider", file)
    }

    companion object {
        private const val FILENAME_FORMAT = "yyyyMMdd_HHmmss"
    }
}