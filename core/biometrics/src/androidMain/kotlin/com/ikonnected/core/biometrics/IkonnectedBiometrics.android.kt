package com.ikonnected.core.biometrics

import androidx.fragment.app.FragmentActivity
import com.ikonnected.core.biometrics.sharedpref.EncryptedIVAndTextStorage
import com.ikonnected.core.sharedpref.AppSharedStorageProvider


actual fun buildIkonnectedBiometrics(
    context: Any,
    appSharedStorageProvider: AppSharedStorageProvider
): BiometricsRequestManager {
    return CoreBiometricsRequestManager(
        activity = context as FragmentActivity,
        encryptionManager = BiometricEncryptionManager(
            sharedPref = EncryptedIVAndTextStorage(
                sharedPref = appSharedStorageProvider
            )
        ),
    )
}
