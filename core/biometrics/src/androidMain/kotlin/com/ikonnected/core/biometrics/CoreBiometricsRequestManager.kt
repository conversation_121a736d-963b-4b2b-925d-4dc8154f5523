package com.ikonnected.core.biometrics

import androidx.biometric.BiometricManager
import androidx.biometric.BiometricPrompt
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import com.ikonnected.core.biometrics.callback.BiometricsResultCallback
import com.ikonnected.core.biometrics.model.BiometricsEnrollmentStatus
import com.ikonnected.core.biometrics.model.BiometricsResult
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import me.tatarka.inject.annotations.Inject
import java.util.concurrent.Executor

internal class CoreBiometricsRequestManager @Inject constructor(
    private val activity: FragmentActivity,
    private val encryptionManager: BiometricEncryptionManager,
    private val coroutineScope: CoroutineScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
) : BiometricsRequestManager {

    private val sharedFlow = MutableSharedFlow<BiometricsResult>()

    override fun request(passphrase: String?): Flow<BiometricsResult> {
        val promptInfo = BiometricPrompt.PromptInfo.Builder()
            .setTitle("Biometric login for database")
            .setSubtitle("Log in using your biometric credential")
            .setNegativeButtonText("Use account password")
            .build()

        val executor = ContextCompat.getMainExecutor(activity.applicationContext)

        if (encryptionManager.hasEncryptedData()) {
            this.decrypt(
                promptInfo = promptInfo,
                fragmentActivity = activity,
                executor = executor
            )
        } else {
            this.encrypt(
                passphrase = requireNotNull(passphrase) { "Passphrase cannot be null for an encryption" },
                promptInfo = promptInfo,
                fragmentActivity = activity,
                executor = executor
            )
        }

        return sharedFlow
    }

    private fun encrypt(
        passphrase: String,
        promptInfo: BiometricPrompt.PromptInfo,
        fragmentActivity: FragmentActivity,
        executor: Executor
    ) {
        val cipher = encryptionManager.getEncryptCipher()
        val dfs = BiometricsResultCallback(
            passPhraseGenerator = { encryptionManager.encryptPassphrase(it, passphrase) },
            {
                coroutineScope.launch {
                    sharedFlow.emit(it)
                }
            }
        )
        val prompt = BiometricPrompt(fragmentActivity, executor, dfs)
        prompt.authenticate(promptInfo, BiometricPrompt.CryptoObject(cipher))
    }

    private fun decrypt(
        promptInfo: BiometricPrompt.PromptInfo,
        fragmentActivity: FragmentActivity,
        executor: Executor
    ) {
        val cipher = encryptionManager.getDecryptCipher()
        val dfs = BiometricsResultCallback(
            passPhraseGenerator = encryptionManager::decryptPassphrase,
            {
                coroutineScope.launch {
                    sharedFlow.emit(it)
                }
            }
        )
        val prompt = BiometricPrompt(fragmentActivity, executor, dfs)
        prompt.authenticate(promptInfo, BiometricPrompt.CryptoObject(cipher))
    }

    override suspend fun canAuthenticateWithBiometrics(): Boolean {
        val biometricManager = BiometricManager.from(activity.applicationContext)
        val authResult =
            biometricManager.canAuthenticate(BiometricManager.Authenticators.BIOMETRIC_STRONG)
        return when {
            authResult == BiometricManager.BIOMETRIC_SUCCESS -> true
            else -> false
        }
    }

    override fun enrollmentStatus(): BiometricsEnrollmentStatus {
        return when (encryptionManager.hasEncryptedData()) {
            true -> BiometricsEnrollmentStatus.ENROLLED
            false -> BiometricsEnrollmentStatus.NOT_ENROLLED
        }
    }

}