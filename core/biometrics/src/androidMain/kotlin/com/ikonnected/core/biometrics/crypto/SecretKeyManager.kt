package com.ikonnected.core.biometrics.crypto

import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import com.ikonnected.core.biometrics.BiometricEncryptionManager.Companion.ENCRYPTION_ALGORITHM
import com.ikonnected.core.biometrics.BiometricEncryptionManager.Companion.ENCRYPTION_BLOCK_MODE
import com.ikonnected.core.biometrics.BiometricEncryptionManager.Companion.ENCRYPTION_PADDING
import me.tatarka.inject.annotations.Inject
import java.security.KeyStore
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey


internal interface ISecretKeyManager {
    fun getSecretKey(): SecretK<PERSON>
}

internal class SecretKeyManager @Inject constructor() : ISecretKeyManager {

    // Retrieve the secret key from the Android Keystore
    override fun getSecretKey(): SecretKey {
        val keyStore = KeyStore.getInstance(ANDROID_KEYSTORE)
        keyStore.load(null)
        return keyStore.getKey(ENCRYPTION_KEY_ALIAS, null) as SecretKey?
            ?: generateSecretKey()
    }

    private fun generateSecretKey(): SecretKey {
        val keyGenerator = KeyGenerator.getInstance(
            ENCRYPTION_ALGORITHM,
            ANDROID_KEYSTORE
        )

        val keyGenParameterSpec = KeyGenParameterSpec.Builder(
            ENCRYPTION_KEY_ALIAS,
            KeyProperties.PURPOSE_ENCRYPT or KeyProperties.PURPOSE_DECRYPT
        ).apply {
            setBlockModes(ENCRYPTION_BLOCK_MODE)
            setEncryptionPaddings(ENCRYPTION_PADDING)
            setUserAuthenticationRequired(true)
            setRandomizedEncryptionRequired(true)
        }.build()

        keyGenerator.init(keyGenParameterSpec)
        return keyGenerator.generateKey()
    }

    companion object {
        private const val ANDROID_KEYSTORE = "AndroidKeyStore"
        private const val ENCRYPTION_KEY_ALIAS = "IkonnectedEncryptionKey"
    }

}
