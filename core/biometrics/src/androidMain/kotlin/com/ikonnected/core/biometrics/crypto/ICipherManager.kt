package com.ikonnected.core.biometrics.crypto

import com.ikonnected.core.biometrics.BiometricEncryptionManager.Companion.ENCRYPTION_ALGORITHM
import com.ikonnected.core.biometrics.BiometricEncryptionManager.Companion.ENCRYPTION_BLOCK_MODE
import com.ikonnected.core.biometrics.BiometricEncryptionManager.Companion.ENCRYPTION_PADDING
import me.tatarka.inject.annotations.Inject
import javax.crypto.Cipher
import javax.crypto.spec.GCMParameterSpec

internal interface ICipherManager {
    fun getEncryptCipher(): Cipher
    fun getDecryptCipher(): Cipher
}

internal class CipherManager @Inject constructor(
    private val encryptedIvAndByteArrayProvider: () -> ByteArray
) : ICipherManager, ISecretKeyManager by SecretKeyManager() {

    // Prepare encryption cipher
    override fun getEncryptCipher(): Cipher {
        val cipher =
            Cipher.getInstance("$ENCRYPTION_ALGORITHM/$ENCRYPTION_BLOCK_MODE/$ENCRYPTION_PADDING")
        cipher.init(Cipher.ENCRYPT_MODE, getSecretKey())
        return cipher
    }

    override fun getDecryptCipher(): Cipher {
        val encryptedIvAndText = encryptedIvAndByteArrayProvider.invoke()
        val cipher =
            Cipher.getInstance("$ENCRYPTION_ALGORITHM/$ENCRYPTION_BLOCK_MODE/$ENCRYPTION_PADDING")
        val iv = encryptedIvAndText.copyOfRange(0, 12)
        cipher.init(Cipher.DECRYPT_MODE, getSecretKey(), GCMParameterSpec(128, iv))
        return cipher
    }

}