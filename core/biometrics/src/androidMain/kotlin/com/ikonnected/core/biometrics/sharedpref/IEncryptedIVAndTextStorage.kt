package com.ikonnected.core.biometrics.sharedpref

import android.util.Base64
import android.util.Log
import com.ikonnected.core.sharedpref.AppSharedStorageProvider


internal interface IEncryptedIVAndTextStorage {
    fun get(): ByteArray
    fun hasEncryptedData(): Boolean
    fun set(encryptedIvAndText: ByteArray): String
}

internal class EncryptedIVAndTextStorage(
    private val sharedPref: AppSharedStorageProvider
) : IEncryptedIVAndTextStorage {

    override fun hasEncryptedData() = sharedPref.get(ENCRYPTED_PREF) != null

    override fun get(): ByteArray {
        val encryptedBase64Text =
            sharedPref.get(ENCRYPTED_PREF) ?: throw IllegalStateException("No saved data")
        val encryptedIvAndText = Base64.decode(encryptedBase64Text, Base64.DEFAULT)
        return encryptedIvAndText
    }

    override fun set(encryptedIvAndText: ByteArray): String {
        // Base64 encode for easy storage and transmission
        val encryptedBase64 = Base64.encodeToString(encryptedIvAndText, Base64.DEFAULT)
        sharedPref.set(ENCRYPTED_PREF, encryptedBase64)
        Log.e("Loka", "Jumpa $encryptedBase64")
        return encryptedBase64
    }

    companion object {
        private const val ENCRYPTED_PREF = "soma+pref"
    }

}