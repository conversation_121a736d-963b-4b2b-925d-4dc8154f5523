package com.ikonnected.core.biometrics

import com.ikonnected.core.biometrics.model.BiometricsEnrollmentStatus
import com.ikonnected.core.biometrics.model.BiometricsResult
import com.ikonnected.core.sharedpref.AppSharedStorageProvider
import kotlinx.coroutines.flow.Flow

interface BiometricsRequestManager {
    /**
     * @param passphrase - this is the passphrase that is to be encrypted.
     * NB: This should only be generated once. Subsequent request should be
     */
    fun request(passphrase: String?): Flow<BiometricsResult>
    suspend fun canAuthenticateWithBiometrics(): Boolean
    fun enrollmentStatus(): BiometricsEnrollmentStatus
}

expect fun buildIkonnectedBiometrics(context: Any, appSharedStorageProvider: AppSharedStorageProvider): BiometricsRequestManager
