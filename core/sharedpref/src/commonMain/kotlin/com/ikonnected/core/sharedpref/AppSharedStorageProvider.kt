package com.ikonnected.core.sharedpref

import kotlinx.serialization.json.Json

interface AppSharedStorageProvider {
    fun get(key: String): String?
    fun set(key: String, value: String): Boolean
    fun clear(key: String): Boolean
}

class KeyValueStorage<T: Any>(
    val pref: AppSharedStorageProvider
)  {

    inline fun <reified T : Any> get(key: String): T? {
        return when (val value = pref.get(key)) {
            is String -> Json.decodeFromString(value)
            else -> null
        }
    }

    inline fun <reified T : Any> set(key: String, value: T): Boolean {
        val jsonString = Json.encodeToString(value = value)
        return pref.set(key, jsonString)
    }
}