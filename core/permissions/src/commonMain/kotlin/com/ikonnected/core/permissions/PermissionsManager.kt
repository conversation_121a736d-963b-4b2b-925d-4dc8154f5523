package com.ikonnected.core.permissions


import com.ikonnected.core.permissions.model.PermissionsRequest
import com.ikonnected.core.permissions.model.PermissionsResult
import kotlinx.coroutines.flow.Flow

interface PermissionsManager {
    fun requestPermission(request: PermissionsRequest): Flow<PermissionsResult>

    /**
     * TODO - To be removed
     * This should be called from the top-level component
     */
    fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>, grantResults: IntArray) {  }

    companion object {
        lateinit var shared : PermissionsManager

        fun initialize(appContext: Any) {
            this.shared = buildPermissionsManager(
                appContext = appContext
            )
        }
    }
}

expect fun buildPermissionsManager(appContext: Any): PermissionsManager
