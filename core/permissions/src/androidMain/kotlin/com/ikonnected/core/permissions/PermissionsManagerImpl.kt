package com.ikonnected.core.permissions
import android.content.pm.PackageManager
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import com.ikonnected.core.permissions.mapper.PermissionRequestMapper
import com.ikonnected.core.permissions.model.PermissionsRequest
import com.ikonnected.core.permissions.model.PermissionsResult
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.launch

internal class PermissionsManagerImpl constructor(
    private val activity: FragmentActivity,
    private val mapper: PermissionRequestMapper,
    private val coroutineScope: CoroutineScope = CoroutineScope(Dispatchers.Unconfined + SupervisorJob())
) : PermissionsManager {

    private val _sharedFlow = MutableSharedFlow<PermissionsResult>()

    private val requestPermissionLauncher =
        activity.registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { result: Map<String, Boolean> ->
            if (isGranted(result = result)) {
                // Permission is granted. Continue the action or workflow in your
                // app.
                coroutineScope.launch {
                    _sharedFlow.emit(PermissionsResult.Success)
                }
            } else {
                // Explain to the user that the feature is unavailable because the
                // feature requires a permission that the user has denied. At the
                // same time, respect the user's decision. Don't link to system
                // settings in an effort to convince the user to change their
                // decision.
                coroutineScope.launch {
                    _sharedFlow.emit(PermissionsResult.Denied)
                }

            }
        }

    override fun requestPermission(request: PermissionsRequest): Flow<PermissionsResult> {
        // Filter out the already granted permissions
        val permissions = request.let(mapper::map).ungranteds()
        return when {
            permissions.isEmpty() -> {
                // You can use the API that requires the permission.
                flowOf(PermissionsResult.Success)
            }
            ActivityCompat.shouldShowRequestPermissionRationale(activity, permissions.first()) && !request.contextUiDisplayed -> {
                // In an educational UI, explain to the user why your app requires this
                // permission for a specific feature to behave as expected, and what
                // features are disabled if it's declined. In this UI, include a
                // "cancel" or "no thanks" button that lets the user continue
                // using your app without granting the permission.
                flowOf(PermissionsResult.ShouldShowPermissionRationale)
            }

            else -> {
                // You can directly ask for the permission.
                // The registered ActivityResultCallback gets the result of this request.
                requestPermissionLauncher.launch(permissions.toTypedArray())
                _sharedFlow
            }
        }
    }

    private fun isGranted(result: Map<String, Boolean>): Boolean {
        val notGranteds =  result.values.filter { !it }
        return notGranteds.isEmpty()
    }

    private fun isPermissionsGranted(permissions: Array<String>): Boolean {
        val grantedPermissions = permissions.count { permission ->
            ContextCompat.checkSelfPermission(
                activity,
                permission
            ) == PackageManager.PERMISSION_GRANTED
        }
        return grantedPermissions == permissions.size
    }

    private fun Array<String>.ungranteds() = this.filter {
        ContextCompat.checkSelfPermission(
            activity,
            it
        ) != PackageManager.PERMISSION_GRANTED
    }

}