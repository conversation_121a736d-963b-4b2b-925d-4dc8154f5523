package com.ikonnected.database

import com.ikonnected.database.dto.LocalInteraction
import kotlinx.coroutines.flow.Flow

interface InteractionDao {
    fun observe(queryText: String?): Flow<List<LocalInteraction>>
    suspend fun get(id: String): LocalInteraction
    suspend fun insert(interaction: LocalInteraction): Boolean
    suspend fun delete(interactionId: String): Boolean
    suspend fun insertAll(interactions: List<LocalInteraction>)
    suspend fun fetchAll(): List<LocalInteraction>
}
