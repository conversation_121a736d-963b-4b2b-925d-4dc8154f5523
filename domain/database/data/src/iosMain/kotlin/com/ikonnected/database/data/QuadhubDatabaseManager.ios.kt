package com.ikonnected.database.data

import app.cash.sqldelight.db.SqlDriver
import me.tatarka.inject.annotations.Inject

actual fun buildDatabaseManager(appContext: Any): IkonnectedDatabaseManager {
    return IkonnectedDatabaseManagerImpl()
}

internal class IkonnectedDatabaseManagerImpl @Inject constructor() : IkonnectedDatabaseManager {
    override fun isOpen(): <PERSON><PERSON><PERSON> {
        TODO("Not yet implemented")
    }

    override fun open(databaseName: String, password: ByteArray): SqlDriver {
        TODO("Not yet implemented")
    }

    override fun destroy(databaseName: String?) {
        TODO("Not yet implemented")
    }
}