package com.ikonnected.database.data

import android.content.Context
import app.cash.sqldelight.driver.android.AndroidSqliteDriver
import me.tatarka.inject.annotations.Inject
import net.zetetic.database.sqlcipher.SupportOpenHelperFactory
import java.io.File
import app.cash.sqldelight.db.SqlDriver

actual fun buildDatabaseManager(appContext: Any): IkonnectedDatabaseManager {
    val applicationContext = appContext as Context
    return IkonnectedDatabaseManagerImpl(
        applicationContext = applicationContext
    )
}

internal class IkonnectedDatabaseManagerImpl @Inject constructor(
    private val applicationContext: Context
) : IkonnectedDatabaseManager {

    private var isOpened: Boolean = false
    private var databaseName: String? = null

    init {
        System.loadLibrary("sqlcipher")
    }

    override fun isOpen(): Boolean {
        return isOpened
    }

    override fun open(databaseName: String, password: ByteArray): SqlDriver {
        this.databaseName = databaseName
        val databaseFile: File = applicationContext.getDatabasePath(databaseName)
        val factory = SupportOpenHelperFactory(password)
        return AndroidSqliteDriver(
            schema = Database.Schema,
            context = applicationContext,
            name = databaseFile.absolutePath,
            factory = factory,
        )
    }

    override fun destroy(databaseName: String?) {
        applicationContext.deleteDatabase(databaseName ?: this.databaseName)
    }
}