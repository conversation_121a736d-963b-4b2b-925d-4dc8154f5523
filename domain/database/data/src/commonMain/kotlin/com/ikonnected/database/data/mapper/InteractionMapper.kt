package com.ikonnected.database.data.mapper

import com.ikonnected.database.data.FetchAll
import com.ikonnected.database.data.InteractionEntity
import com.ikonnected.database.data.InteractionPersonEntity
import com.ikonnected.database.data.FetchById
import com.ikonnected.database.dto.LocalInteraction
import com.ikonnected.database.dto.InteractionUser
import me.tatarka.inject.annotations.Inject

internal interface InteractionMapper {
    fun mapTo(entity: FetchById): LocalInteraction
    fun mapFrom(dto: LocalInteraction): InteractionEntity
    fun toUser(user: InteractionUser): InteractionPersonEntity
    fun fromUser(entity: InteractionPersonEntity): InteractionUser
    fun mapFromFetchAll(fetchAll: FetchAll): LocalInteraction
    fun mapFromFetchById(fetchById: FetchById): LocalInteraction
}

internal class InteractionMapperImpl @Inject constructor() : InteractionMapper {

    override fun mapTo(entity: FetchById): LocalInteraction {
        return with(entity) {
            LocalInteraction(
                id = _id,
                createdTime = created_time ?: "",
                userId = user_id ?: "",
                postPerson = null, // Will be populated separately with join queries
                otherPersons = null, // Will be populated separately with join queries
                status = status
            )
        }
    }

    override fun mapFrom(dto: LocalInteraction): InteractionEntity {
        return with(dto) {
            InteractionEntity(
                _id = id,
                created_time = createdTime,
                user_id = userId,
                status = status,
                post_person = postPerson?.id
            )
        }
    }

    override fun toUser(user: InteractionUser): InteractionPersonEntity {
        return with(user) {
            InteractionPersonEntity(
                _id = id,
                first_name = firstName,
                last_name = lastName
            )
        }
    }

    override fun fromUser(entity: InteractionPersonEntity): InteractionUser {
        return with(entity) {
            InteractionUser(
                id = _id,
                firstName = first_name ?: "",
                lastName = last_name ?: ""
            )
        }
    }

    override fun mapFromFetchAll(fetchAll: FetchAll): LocalInteraction {
        return with(fetchAll) {
            LocalInteraction(
                id = _id,
                createdTime = created_time ?: "",
                userId = user_id ?: "",
                postPerson = if (_id_ != null) {
                    InteractionUser(
                        id = _id_,
                        firstName = first_name ?: "",
                        lastName = last_name ?: ""
                    )
                } else null,
                otherPersons = null, // Will be populated separately
                status = status
            )
        }
    }

    override fun mapFromFetchById(fetchById: FetchById): LocalInteraction {
        return with(fetchById) {
            LocalInteraction(
                id = _id,
                createdTime = created_time ?: "",
                userId = user_id ?: "",
                postPerson = if (_id_ != null) {
                    InteractionUser(
                        id = _id_,
                        firstName = first_name ?: "",
                        lastName = last_name ?: ""
                    )
                } else null,
                otherPersons = null, // Will be populated separately
                status = status
            )
        }
    }
}