package com.ikonnected.database.data.mapper

import com.ikonnected.database.data.ProfileEntity
import com.ikonnected.database.dto.LocalUserProfile
import me.tatarka.inject.annotations.Inject

internal interface ProfileMapper {
    fun map(entity: ProfileEntity): LocalUserProfile
    fun mapToEntity(dto: LocalUserProfile): ProfileEntity
}

internal class ProfileMapperImpl @Inject constructor() : ProfileMapper {

    override fun map(entity: ProfileEntity): LocalUserProfile {
        return with(entity) {
            LocalUserProfile(
                id = _id,
                createdTime = created_time,
                userId = requireNotNull(user_id),
                email = requireNotNull(email),
                firstName = first_name,
                university = university,
                lastName = last_name
            )
        }
    }

    override fun mapToEntity(dto: LocalUserProfile): ProfileEntity {
        return with(dto) {
            ProfileEntity(
                _id = id,
                created_time = createdTime,
                user_id = userId,
                email = email,
                first_name = firstName,
                last_name = lastName,
                university = university
            )
        }
    }

}