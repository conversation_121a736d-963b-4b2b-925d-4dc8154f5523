package com.ikonnected.database.data

import app.cash.sqldelight.coroutines.asFlow
import app.cash.sqldelight.coroutines.mapToOne
import com.ikonnected.database.ProfileDao
import com.ikonnected.database.data.mapper.ProfileMapper
import com.ikonnected.database.dto.LocalUserProfile
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import me.tatarka.inject.annotations.Inject

internal class ProfileDaoImpl @Inject constructor(
    private val mapper: ProfileMapper,
) : ProfileDao {

    private val profileQueries: ProfileQueries by lazy  { ProfileQueries(driver = IkonnectedDatabaseManager.driver) }

    override fun getProfile(): LocalUserProfile {
        val entity = profileQueries.fetchAll().executeAsList().first()
        return mapper.map(entity = entity)
    }

    override fun observe(userId: String): Flow<LocalUserProfile> {
        return profileQueries
            .fetchById(userId)
            .asFlow()
            .mapToOne(Dispatchers.Default)
            .map(mapper::map)
    }

    override suspend fun insert(profile: LocalUserProfile) {
        profileQueries.insert(mapper.mapToEntity(profile))
    }

    override suspend fun setUserId(userId: String) {
        val profile = profileQueries.fetchAll().executeAsList().first()
        profileQueries.updateUserId(userId = userId, id = profile._id)
    }

    override fun getUserId(): String? {
        return profileQueries.fetchAll().executeAsList().firstOrNull()?.user_id
    }

    override fun clearAll() {
        return profileQueries.deleteAll()
    }

}