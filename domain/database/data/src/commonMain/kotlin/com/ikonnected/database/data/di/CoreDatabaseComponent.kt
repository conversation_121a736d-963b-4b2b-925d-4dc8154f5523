package com.ikonnected.database.data.di

import com.ikonnected.database.DaoProvider
import com.ikonnected.database.InteractionDao
import com.ikonnected.database.ProfileDao
import com.ikonnected.database.data.InteractionDaoImpl
import com.ikonnected.database.data.ProfileDaoImpl
import com.ikonnected.database.data.mapper.InteractionMapperImpl
import com.ikonnected.database.data.mapper.ProfileMapperImpl
import me.tatarka.inject.annotations.Component
import me.tatarka.inject.annotations.Provides

@Component
abstract class CoreDatabaseComponent : DaoProvider {

    @Provides
    fun provideInteractionDao(): InteractionDao = InteractionDaoImpl(
        mapper = InteractionMapperImpl()
    )

    @Provides
    fun provideProfileDao(): ProfileDao = ProfileDaoImpl(
        mapper = ProfileMapperImpl()
    )

}
