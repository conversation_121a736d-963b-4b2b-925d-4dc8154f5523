CREATE TABLE InteractionEntity (
    _id TEXT PRIMARY KEY NOT NULL,
    created_time TEXT,
    user_id TEXT,
    status TEXT,
    post_person TEXT,
    FOREIGN KEY (post_person) REFERENCES InteractionPersonEntity(_id)
);

CREATE TABLE InteractionPersonEntity (
    _id TEXT PRIMARY KEY NOT NULL,
    first_name TEXT,
    last_name TEXT
);

-- Junction table for other persons (many-to-many relationship)
CREATE TABLE InteractionOtherPersons (
    interaction_id TEXT NOT NULL,
    person_id TEXT NOT NULL,
    PRIMARY KEY (interaction_id, person_id),
    FOREIGN KEY (interaction_id) REFERENCES InteractionEntity(_id),
    FOREIGN KEY (person_id) REFERENCES InteractionPersonEntity(_id)
);

fetchAll:
SELECT ie.*, ipe.* FROM InteractionEntity ie
LEFT JOIN InteractionPersonEntity ipe ON ie.post_person == ipe._id;

searchAll:
SELECT * FROM InteractionEntity WHERE status LIKE :queryText OR _id LIKE :queryText;

insert:
INSERT OR REPLACE INTO InteractionEntity(_id, created_time, user_id, status, post_person)
VALUES ?;

insertPerson:
INSERT OR REPLACE INTO InteractionPersonEntity(_id, first_name, last_name)
VALUES ?;

insertOtherPerson:
INSERT OR REPLACE INTO InteractionOtherPersons(interaction_id, person_id)
VALUES (?, ?);

fetchById:
SELECT ie.*, ipe.* FROM InteractionEntity ie
LEFT JOIN InteractionPersonEntity ipe ON ie.post_person == ipe._id
WHERE ie._id == :id;

fetchOtherPersons:
SELECT ipe.* FROM InteractionOtherPersons iop
JOIN InteractionPersonEntity ipe ON iop.person_id == ipe._id
WHERE iop.interaction_id == :interactionId;

deleteAll:
DELETE FROM InteractionEntity;

deleteById:
DELETE FROM InteractionEntity WHERE _id == :id;