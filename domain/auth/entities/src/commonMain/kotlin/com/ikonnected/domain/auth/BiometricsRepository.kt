package com.ikonnected.domain.auth

import kotlinx.coroutines.flow.Flow

interface BiometricsRepository {
    fun observeBiometrics(): Flow<BiometricsEntityResult>
    suspend fun biometricsAvailability(): BiometricsAvailabilityEntityResult

    suspend fun biometricsEnrollment(openDatabase: Boolean = false): BiometricsEnrollmentEntityResult

    suspend fun skipBiometrics(): SkipBiometricsEntityResult
}