package com.ikonnected.domain.auth

sealed class BiometricsEntityResult {

    data object Success : BiometricsEntityResult()
    data object Failure : BiometricsEntityResult()

}

sealed class BiometricsAvailabilityEntityResult {
    data object Success : BiometricsAvailabilityEntityResult()
    data object Failure : BiometricsAvailabilityEntityResult()
}

sealed class SkipBiometricsEntityResult {
    data object Success : SkipBiometricsEntityResult()
    data object Failure : SkipBiometricsEntityResult()
}
