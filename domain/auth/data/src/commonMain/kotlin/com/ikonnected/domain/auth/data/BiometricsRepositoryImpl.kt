package com.ikonnected.domain.auth.data

import com.ikonnected.core.biometrics.BiometricsRequestManager
import com.ikonnected.core.biometrics.model.BiometricsEnrollmentStatus
import com.ikonnected.core.biometrics.model.BiometricsResult
import com.ikonnected.domain.auth.BiometricsAvailabilityEntityResult
import com.ikonnected.domain.auth.BiometricsEnrollmentEntityResult
import com.ikonnected.domain.auth.BiometricsEntityResult
import com.ikonnected.domain.auth.BiometricsRepository
import com.ikonnected.domain.auth.SkipBiometricsEntityResult
import com.ikonnected.domain.auth.data.datasource.local.DatabaseInitializationListener
import com.ikonnected.core.log.ErrorLogger
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import me.tatarka.inject.annotations.Inject

internal class BiometricsRepositoryImpl @Inject constructor(
    private val biometricsManager: BiometricsRequestManager,
    private val dbInitializer: DatabaseInitializationListener,
    private val errorLogger: ErrorLogger
) : BiometricsRepository {

    override fun observeBiometrics(): Flow<BiometricsEntityResult> {
        val passphrase = when (isBiometricsEnabled()) {
            true -> null
            false -> generateDatabasePassphrase()
        }

        return biometricsManager.request(passphrase = passphrase).map {
            when (it) {
                is BiometricsResult.Success -> {
                    // Use the biometrics result to initialize the database
                    val actualPassphrase = passphrase ?: it.secretKey
                    dbInitializer.open(password = actualPassphrase)
                    BiometricsEntityResult.Success
                }
                else -> BiometricsEntityResult.Failure
            }
        }
    }

    override suspend fun biometricsAvailability(): BiometricsAvailabilityEntityResult {
        return when (biometricsManager.canAuthenticateWithBiometrics()) {
            true -> BiometricsAvailabilityEntityResult.Success
            false -> BiometricsAvailabilityEntityResult.Failure
        }
    }

    override suspend fun biometricsEnrollment(openDatabase: Boolean): BiometricsEnrollmentEntityResult {
        return when (isBiometricsEnabled()) {
            true -> BiometricsEnrollmentEntityResult.Enrolled
            false -> {
                if (openDatabase) {
                    // if the user is not enrolled. Try to open the database
                    dbInitializer.open(password = DEFAULT_PASSPHRASE)
                }
                BiometricsEnrollmentEntityResult.UnEnrolled
            }
        }
    }

    override suspend fun skipBiometrics(): SkipBiometricsEntityResult {
        return try {
            dbInitializer.open(password = DEFAULT_PASSPHRASE)
            SkipBiometricsEntityResult.Success
        } catch (exception: Exception) {
            errorLogger.log(exception)
            SkipBiometricsEntityResult.Failure
        }
    }

    private fun isBiometricsEnabled(): Boolean {
        return when (biometricsManager.enrollmentStatus()) {
            BiometricsEnrollmentStatus.ENROLLED -> true
            else -> false
        }
    }

    private fun generateDatabasePassphrase(): String {
        return "passphrase"
    }

    companion object {
        private const val DEFAULT_PASSPHRASE = "passphrase"
    }

}