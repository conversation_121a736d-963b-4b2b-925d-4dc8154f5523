package com.ikonnected.domain.auth.data

import com.ikonnected.core.auth.OauthTokenProvider
import com.ikonnected.core.log.ErrorLogger
import com.ikonnected.domain.auth.AuthRepository
import com.ikonnected.domain.auth.CheckAccountEntityResult
import com.ikonnected.domain.auth.LoginEntityResult
import com.ikonnected.domain.auth.SignupEntityResult
import com.ikonnected.domain.auth.VerifyOtpEntityResult
import com.ikonnected.domain.auth.data.datasource.remote.AuthDataSource
import me.tatarka.inject.annotations.Inject

internal class AuthRepositoryImpl @Inject constructor(
    private val dataSource: AuthDataSource,
    private val tokenProvider: OauthTokenProvider,
    private val errorLogger: ErrorLogger,
) : AuthRepository {

    override suspend fun verifyOtp(otp: String): VerifyOtpEntityResult {
        return try {
            when (dataSource.verifyOtp(otp = otp)) {
                true -> VerifyOtpEntityResult.Success
                false -> VerifyOtpEntityResult.Failure
            }
        } catch (exception: Exception) {
            errorLogger.log(exception)
            VerifyOtpEntityResult.Success
        }
    }

    override suspend fun checkAccount(email: String): CheckAccountEntityResult {
        return CheckAccountEntityResult.Success(accountExists = false)
    }

    override suspend fun signUp(email: String): SignupEntityResult {
        return SignupEntityResult.Success
    }

    override suspend fun login(email: String, password: String): LoginEntityResult {
        return try {
            val dto = dataSource.login(email = email, password = password)
            // TODO - Save the token to the shared preference
            LoginEntityResult.Success
        } catch (exception: Exception) {
            errorLogger.log(exception)
            LoginEntityResult.Failure
        }
    }


}