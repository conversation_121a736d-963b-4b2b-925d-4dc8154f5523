package com.ikonnected.domain.auth.data.di

import com.ikonnected.core.auth.OauthTokenProvider
import com.ikonnected.core.biometrics.BiometricsRequestManager
import com.ikonnected.core.log.ErrorLogger
import com.ikonnected.domain.auth.AuthRepositories
import com.ikonnected.domain.auth.AuthRepository
import com.ikonnected.domain.auth.BiometricsRepository
import com.ikonnected.domain.auth.data.AuthRepositoryImpl
import com.ikonnected.domain.auth.data.BiometricsRepositoryImpl
import com.ikonnected.domain.auth.data.datasource.api.AuthServiceImpl
import com.ikonnected.domain.auth.data.datasource.local.DatabaseInitializationListener
import com.ikonnected.domain.auth.data.datasource.remote.AuthDataSourceImpl
import com.ikonnected.domain.auth.data.datasource.remote.mapper.AuthDtoMapperImpl
import io.ktor.client.HttpClient
import me.tatarka.inject.annotations.Component
import me.tatarka.inject.annotations.Provides

@Component
abstract class AuthDomainComponent(
    private val client: HttpClient,
    private val errorLogger: ErrorLogger,
    private val tokenProvider: OauthTokenProvider,
    private val biometricsManager: BiometricsRequestManager,
    private val dbInitializer: DatabaseInitializationListener,
) : AuthRepositories {

    @Provides
    fun provideAuthRepository(): AuthRepository {
        return AuthRepositoryImpl(
            dataSource = AuthDataSourceImpl(
                service = AuthServiceImpl(httpClient = client),
                mapper = AuthDtoMapperImpl()
            ),
            errorLogger = errorLogger,
            tokenProvider = tokenProvider,
        )
    }

    @Provides
    fun provideBiometricsRepository(): BiometricsRepository = BiometricsRepositoryImpl(
        biometricsManager = biometricsManager,
        dbInitializer = dbInitializer,
        errorLogger = errorLogger
    )

}