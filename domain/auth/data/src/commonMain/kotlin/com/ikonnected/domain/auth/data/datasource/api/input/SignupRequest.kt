package com.ikonnected.domain.auth.data.datasource.api.input

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
internal data class SignupRequest(
    @SerialName(value = "firstName")
    val firstName: String,
    @SerialName(value = "lastName")
    val lastName: String,
    @SerialName(value = "email")
    val email: String,
    @SerialName(value = "password")
    val password: String
)