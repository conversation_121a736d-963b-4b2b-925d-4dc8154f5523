rootProject.name = "iKonnected"
enableFeaturePreview("TYPESAFE_PROJECT_ACCESSORS")

pluginManagement {
    repositories {
        google {
            mavenContent {
                includeGroupAndSubgroups("androidx")
                includeGroupAndSubgroups("com.android")
                includeGroupAndSubgroups("com.google")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}

dependencyResolutionManagement {
    repositories {
        google {
            mavenContent {
                includeGroupAndSubgroups("androidx")
                includeGroupAndSubgroups("com.android")
                includeGroupAndSubgroups("com.google")
            }
        }
        mavenCentral()
    }
}

include(":composeApp")
include(":compose-ui")

include(":core:auth")
include(":core:biometrics")
include(":core:datetime")
include(":core:discopes")
include(":core:file")
include(":core:location")
include(":core:log")
include(":core:network")
include(":core:permissions")
include(":core:presentation")
include(":core:sharedpref")

include(":domain:auth:data")
include(":domain:auth:entities")
include(":domain:database:entities")
include(":domain:database:data")
include(":domain:interactions:entities")
include(":domain:interactions:data")
include(":domain:profile:entities")
include(":domain:profile:data")

include(":features:auth:ui")
include(":features:auth:ui-api")
include(":features:auth:usecase")